# K210简化摄像头测试 - 解决图像格式问题
# 最小化代码，最大化兼容性

import sensor, image, time, lcd

def safe_init():
    """安全的初始化流程"""
    print("开始安全初始化...")
    
    # 初始化LCD
    try:
        lcd.init()
        lcd.clear(lcd.BLACK)
        print("✅ LCD初始化成功")
    except Exception as e:
        print("❌ LCD初始化失败:", str(e))
        return False
    
    # 重置摄像头
    try:
        sensor.reset()
        print("✅ 摄像头重置成功")
    except Exception as e:
        print("❌ 摄像头重置失败:", str(e))
        return False
    
    # 设置像素格式 - 优先级顺序
    pixel_formats = [
        (sensor.RGB565, "RGB565"),
        (sensor.GRAYSCALE, "GRAYSCALE")
    ]
    
    format_set = False
    for fmt, name in pixel_formats:
        try:
            sensor.set_pixformat(fmt)
            print("✅ 像素格式设置成功: {}".format(name))
            format_set = True
            break
        except Exception as e:
            print("⚠️ {}格式失败: {}".format(name, str(e)))
    
    if not format_set:
        print("❌ 所有像素格式都失败")
        return False
    
    # 设置分辨率 - 优先级顺序
    frame_sizes = [
        (sensor.QQVGA, "QQVGA(160x120)"),
        (sensor.QVGA, "QVGA(320x240)")
    ]
    
    size_set = False
    for size, name in frame_sizes:
        try:
            sensor.set_framesize(size)
            print("✅ 分辨率设置成功: {}".format(name))
            size_set = True
            break
        except Exception as e:
            print("⚠️ {}分辨率失败: {}".format(name, str(e)))
    
    if not size_set:
        print("❌ 所有分辨率都失败")
        return False
    
    # 其他设置(可选)
    try:
        sensor.set_vflip(1)
        print("✅ 垂直翻转设置成功")
    except:
        print("⚠️ 垂直翻转设置失败(忽略)")
    
    # 等待稳定
    try:
        sensor.skip_frames(time=3000)  # 3秒等待
        sensor.run(1)
        print("✅ 摄像头启动成功")
    except Exception as e:
        print("❌ 摄像头启动失败:", str(e))
        return False
    
    return True

def safe_snapshot():
    """安全的图像获取"""
    max_retries = 3
    
    for i in range(max_retries):
        try:
            img = sensor.snapshot()
            
            if img is None:
                print("⚠️ 图像为空，重试 {}/{}".format(i+1, max_retries))
                time.sleep_ms(100)
                continue
            
            # 检查图像有效性
            try:
                w = img.width()
                h = img.height()
                
                if w <= 0 or h <= 0:
                    print("⚠️ 图像尺寸无效: {}x{}，重试 {}/{}".format(w, h, i+1, max_retries))
                    time.sleep_ms(100)
                    continue
                
                return img
                
            except Exception as e:
                print("⚠️ 图像检查失败: {}，重试 {}/{}".format(str(e), i+1, max_retries))
                time.sleep_ms(100)
                continue
                
        except Exception as e:
            print("⚠️ 图像获取失败: {}，重试 {}/{}".format(str(e), i+1, max_retries))
            time.sleep_ms(100)
            continue
    
    print("❌ 图像获取失败，已重试{}次".format(max_retries))
    return None

def safe_display(img):
    """安全的图像显示"""
    try:
        lcd.display(img)
        return True
    except Exception as e:
        print("❌ 图像显示失败:", str(e))
        return False

def run_simple_test():
    """运行简单测试"""
    print("开始简单摄像头测试...")
    
    frame_count = 0
    success_count = 0
    
    try:
        while frame_count < 50:  # 测试50帧
            img = safe_snapshot()
            
            if img:
                try:
                    # 简单的信息显示
                    w = img.width()
                    h = img.height()
                    
                    # 绘制基本信息
                    img.draw_string(5, 5, "Frame:{}".format(frame_count), 
                                   color=(255, 255, 255), scale=1)
                    img.draw_string(5, 25, "Size:{}x{}".format(w, h), 
                                   color=(0, 255, 0), scale=1)
                    img.draw_string(5, 45, "OK:{}".format(success_count), 
                                   color=(0, 255, 255), scale=1)
                    
                    # 显示图像
                    if safe_display(img):
                        success_count += 1
                        if frame_count % 10 == 0:
                            print("✅ 第{}帧成功 (成功率: {:.1f}%)".format(
                                frame_count, success_count*100/max(1, frame_count)))
                    
                except Exception as e:
                    print("⚠️ 第{}帧处理失败: {}".format(frame_count, str(e)))
            
            frame_count += 1
            time.sleep_ms(200)  # 200ms间隔
            
    except KeyboardInterrupt:
        print("测试被用户中断")
    except Exception as e:
        print("❌ 测试过程出错:", str(e))
    
    # 显示结果
    success_rate = success_count * 100 / max(1, frame_count)
    print("\n📊 测试结果:")
    print("总帧数: {}".format(frame_count))
    print("成功帧数: {}".format(success_count))
    print("成功率: {:.1f}%".format(success_rate))
    
    if success_rate > 80:
        print("🎉 摄像头工作正常!")
        return True
    elif success_rate > 50:
        print("⚠️ 摄像头工作不稳定，建议检查连接")
        return False
    else:
        print("❌ 摄像头工作异常，请检查硬件")
        return False

def show_usage():
    """显示使用说明"""
    print("K210简化摄像头测试工具")
    print("=" * 30)
    print("功能:")
    print("1. 自动检测兼容的图像格式")
    print("2. 自动选择合适的分辨率")
    print("3. 安全的图像获取和显示")
    print("4. 详细的错误信息")
    print("\n如果遇到'Image format is not supported'错误:")
    print("1. 运行此脚本进行诊断")
    print("2. 检查硬件连接")
    print("3. 尝试不同的固件版本")

def main():
    """主函数"""
    show_usage()
    
    # 初始化
    if not safe_init():
        print("❌ 初始化失败，无法继续")
        return
    
    print("\n✅ 初始化成功，开始测试...")
    
    # 运行测试
    if run_simple_test():
        print("\n🎉 测试完成，摄像头工作正常!")
        print("可以运行其他程序:")
        print("exec(open('k210_demo_no_model.py').read())")
    else:
        print("\n❌ 测试失败，建议:")
        print("1. 检查摄像头连接")
        print("2. 重启开发板")
        print("3. 尝试其他固件")
        print("4. 运行完整诊断:")
        print("exec(open('image_format_fix.py').read())")

if __name__ == "__main__":
    main()
