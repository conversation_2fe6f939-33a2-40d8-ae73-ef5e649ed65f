# K210人脸识别代码
import sensor, image, time, lcd, KPU as kpu
from machine import UART
from fpioa_manager import fm
from config import Config

class FaceDetector:
    def __init__(self):
        self.task = None
        self.init_hardware()
        self.load_model()
    
    def init_hardware(self):
        """初始化硬件设备"""
        try:
            lcd.init()  # 初始化LCD
            lcd.clear(lcd.BLACK)
            sensor.reset()  # 重置摄像头
            sensor.set_pixformat(sensor.RGB565)  # 设置像素格式
            sensor.set_framesize(sensor.QVGA)    # 设置帧大小
            sensor.set_windowing((Config.CAMERA_WIDTH, Config.CAMERA_HEIGHT))  # 设置窗口
            sensor.set_vflip(Config.CAMERA_VFLIP)  # 垂直翻转
            sensor.run(1)  # 开始运行
            print("硬件初始化完成")
        except Exception as e:
            print(f"硬件初始化失败: {e}")
            raise
    
    def load_model(self):
        """加载人脸检测模型"""
        try:
            self.task = kpu.load(Config.MODEL_PATH)
            kpu.init_yolo2(self.task, Config.DETECTION_THRESHOLD, Config.NMS_THRESHOLD, Config.ANCHOR_NUM, Config.ANCHOR_POINTS)
            print("模型加载成功")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def detect_faces(self, img):
        """检测人脸"""
        try:
            code = kpu.run_yolo2(self.task, img)
            if code:
                faces = []
                for i in code:
                    face_info = {
                        'x': i.x(),
                        'y': i.y(), 
                        'w': i.w(),
                        'h': i.h(),
                        'confidence': i.value()
                    }
                    if face_info['confidence'] > Config.DETECTION_THRESHOLD:
                        faces.append(face_info)
                return faces
            return []
        except Exception as e:
            print(f"人脸检测错误: {e}")
            return []
    
    def draw_face_box(self, img, faces):
        """绘制人脸框"""
        for face in faces:
            x, y, w, h = face['x'], face['y'], face['w'], face['h']
            confidence = face['confidence']
            
            # 绘制矩形框
            img.draw_rectangle(x, y, w, h, color=Config.BOX_COLOR, thickness=Config.BOX_THICKNESS)

            # 显示置信度
            confidence_text = f"{confidence:.2f}"
            img.draw_string(x, y-20, confidence_text, color=Config.TEXT_COLOR, scale=Config.TEXT_SCALE)
            
            print(f"检测到人脸: 位置({x},{y}) 大小({w}x{h}) 置信度:{confidence:.3f}")
    
    def run(self):
        """主运行循环"""
        print("开始人脸识别...")
        frame_count = 0
        
        try:
            while True:
                img = sensor.snapshot()  # 获取图像
                
                if img:
                    # 检测人脸
                    faces = self.detect_faces(img)
                    
                    # 绘制检测结果
                    if faces:
                        self.draw_face_box(img, faces)
                        print(f"第{frame_count}帧: 检测到{len(faces)}个人脸")
                    
                    # 显示图像
                    lcd.display(img)
                    
                    frame_count += 1
                    
                    # 简单的帧率控制
                    time.sleep_ms(Config.FRAME_DELAY_MS)
                
        except KeyboardInterrupt:
            print("程序被用户中断")
        except Exception as e:
            print(f"运行时错误: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.task:
                kpu.deinit(self.task)
            sensor.shutdown()
            lcd.clear()
            print("资源清理完成")
        except Exception as e:
            print(f"清理资源时出错: {e}")

def main():
    """主函数"""
    try:
        detector = FaceDetector()
        detector.run()
    except Exception as e:
        print(f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
