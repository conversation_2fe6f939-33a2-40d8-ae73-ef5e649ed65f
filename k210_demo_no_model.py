# K210 CanMV演示版 - 无需模型文件
# 展示摄像头和基础图像处理功能

import sensor, image, time, lcd
import gc

def init_hardware():
    """初始化硬件设备"""
    print("初始化LCD...")
    lcd.init()
    lcd.clear(lcd.BLACK)
    
    print("初始化摄像头...")
    sensor.reset()
    sensor.set_pixformat(sensor.RGB565)
    sensor.set_framesize(sensor.QVGA)  # 320x240
    sensor.set_vflip(1)  # 垂直翻转
    sensor.skip_frames(time=1000)
    sensor.run(1)
    
    print("✅ 硬件初始化完成")

def draw_face_frame(img, x, y, w, h, color=(0, 255, 0)):
    """绘制人脸框架"""
    # 绘制矩形框
    img.draw_rectangle(x, y, w, h, color=color, thickness=2)
    
    # 绘制角标
    corner_len = min(w, h) // 4
    # 左上角
    img.draw_line(x, y, x + corner_len, y, color=color, thickness=3)
    img.draw_line(x, y, x, y + corner_len, color=color, thickness=3)
    # 右上角
    img.draw_line(x + w, y, x + w - corner_len, y, color=color, thickness=3)
    img.draw_line(x + w, y, x + w, y + corner_len, color=color, thickness=3)
    # 左下角
    img.draw_line(x, y + h, x + corner_len, y + h, color=color, thickness=3)
    img.draw_line(x, y + h, x, y + h - corner_len, color=color, thickness=3)
    # 右下角
    img.draw_line(x + w, y + h, x + w - corner_len, y + h, color=color, thickness=3)
    img.draw_line(x + w, y + h, x + w, y + h - corner_len, color=color, thickness=3)

def simple_face_detection(img):
    """简单的人脸区域检测演示(基于图像处理)"""
    # 这里使用简单的图像处理方法模拟人脸检测
    # 实际应用中应该使用AI模型
    
    # 转换为灰度图进行处理
    gray = img.to_grayscale()
    
    # 查找可能的人脸区域(这里用简单的矩形区域演示)
    width = img.width()
    height = img.height()
    
    # 模拟检测结果 - 在图像中心区域
    face_w = width // 3
    face_h = height // 3
    face_x = (width - face_w) // 2
    face_y = (height - face_h) // 2
    
    return [(face_x, face_y, face_w, face_h)]

def run_demo():
    """运行演示程序"""
    clock = time.clock()
    frame_count = 0
    demo_mode = 0  # 演示模式: 0=基础显示, 1=边缘检测, 2=模拟人脸框
    mode_names = ["基础显示", "边缘检测", "模拟人脸框"]
    
    print("🎬 开始演示程序...")
    print("演示模式会每10秒自动切换")
    print("按Ctrl+C停止程序")
    
    try:
        while True:
            clock.tick()
            img = sensor.snapshot()
            
            if not img:
                continue
            
            # 每300帧(约10秒)切换一次模式
            if frame_count % 300 == 0:
                demo_mode = (demo_mode + 1) % 3
                print("切换到模式: {}".format(mode_names[demo_mode]))
            
            # 根据模式处理图像
            if demo_mode == 0:
                # 基础显示模式
                img.draw_string(10, 10, "基础显示", color=(255, 255, 255), scale=2)
                img.draw_string(10, 40, "CanMV Demo", color=(0, 255, 0), scale=1)
                
            elif demo_mode == 1:
                # 边缘检测模式
                img.find_edges(image.EDGE_CANNY, threshold=(50, 80))
                img.draw_string(10, 10, "边缘检测", color=(255, 255, 255), scale=2)
                
            elif demo_mode == 2:
                # 模拟人脸框模式
                faces = simple_face_detection(img)
                for i, (x, y, w, h) in enumerate(faces):
                    draw_face_frame(img, x, y, w, h, color=(0, 255, 0))
                    img.draw_string(x, y-25, "模拟人脸{}".format(i+1), color=(255, 0, 0), scale=1)
                
                img.draw_string(10, 10, "模拟检测", color=(255, 255, 255), scale=2)
            
            # 显示帧率和帧数
            fps = clock.fps()
            img.draw_string(10, 220, "FPS:{:.1f} 帧:{}".format(fps, frame_count), 
                          color=(255, 255, 0), scale=1)
            
            # 显示当前模式
            img.draw_string(200, 10, mode_names[demo_mode], color=(255, 0, 255), scale=1)
            
            # 显示图像
            lcd.display(img)
            
            # 垃圾回收
            if frame_count % 30 == 0:  # 每30帧执行一次
                gc.collect()
            
            frame_count += 1
            
            # 简单的帧率控制
            time.sleep_ms(33)  # 约30FPS
            
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print("演示过程出错:", str(e))

def show_info():
    """显示信息界面"""
    print("📱 CanMV K210演示程序")
    print("=" * 30)
    print("功能说明:")
    print("1. 基础显示 - 显示摄像头实时图像")
    print("2. 边缘检测 - 图像边缘检测演示") 
    print("3. 模拟人脸框 - 模拟人脸检测框")
    print("\n💡 提示:")
    print("- 无需模型文件即可运行")
    print("- 演示基础图像处理功能")
    print("- 验证硬件工作状态")
    print("- 每10秒自动切换演示模式")
    print("\n🔧 如需真实人脸识别:")
    print("请下载模型文件并使用:")
    print("exec(open('k210_canmv_face.py').read())")

def cleanup():
    """清理资源"""
    try:
        sensor.shutdown()
        lcd.clear()
        print("✅ 资源清理完成")
    except Exception as e:
        print("清理资源时出错:", str(e))

def main():
    """主函数"""
    show_info()
    
    try:
        # 初始化硬件
        init_hardware()
        
        # 运行演示
        run_demo()
        
    except Exception as e:
        print("❌ 程序运行出错:", str(e))
    finally:
        # 清理资源
        cleanup()

if __name__ == "__main__":
    main()
