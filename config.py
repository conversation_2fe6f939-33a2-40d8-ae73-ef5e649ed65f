# K210人脸识别配置文件
class Config:
    # 模型配置
    MODEL_PATH = "/sd/face_detection.kmodel"  # 人脸检测模型路径
    DETECTION_THRESHOLD = 0.7  # 检测阈值
    NMS_THRESHOLD = 0.3  # 非极大值抑制阈值
    ANCHOR_NUM = 5  # 锚点数量
    
    # 显示配置
    LCD_WIDTH = 320   # LCD宽度
    LCD_HEIGHT = 240  # LCD高度
    
    # 摄像头配置
    CAMERA_WIDTH = 320   # 摄像头宽度  
    CAMERA_HEIGHT = 240  # 摄像头高度
    CAMERA_VFLIP = 1     # 垂直翻转
    
    # 绘制配置
    BOX_COLOR = (0, 255, 0)      # 人脸框颜色(绿色)
    TEXT_COLOR = (255, 255, 255)  # 文字颜色(白色)
    BOX_THICKNESS = 2            # 框线粗细
    TEXT_SCALE = 2               # 文字大小
    
    # 性能配置
    FRAME_DELAY_MS = 50  # 帧间延迟(毫秒)
    
    # YOLO锚点配置
    ANCHOR_POINTS = (1.889, 2.5245, 2.9465, 3.94056, 3.99987, 5.3658, 5.155437, 6.92275, 6.718375, 9.01025)
