# K210人脸识别配置文件
class Config:
    # 模型配置
    MODEL_PATH = "/sd/face_detection.kmodel"  # 人脸检测模型路径
    DETECTION_THRESHOLD = 0.7  # 检测阈值
    NMS_THRESHOLD = 0.3  # 非极大值抑制阈值
    ANCHOR_NUM = 5  # 锚点数量
    
    # 显示配置
    LCD_WIDTH = 320   # LCD宽度
    LCD_HEIGHT = 240  # LCD高度
    
    # 摄像头配置
    CAMERA_WIDTH = 320   # 摄像头宽度  
    CAMERA_HEIGHT = 240  # 摄像头高度
    CAMERA_VFLIP = 1     # 垂直翻转
    
    # 绘制配置
    BOX_COLOR = (0, 255, 0)      # 人脸框颜色(绿色)
    TEXT_COLOR = (255, 255, 255)  # 文字颜色(白色)
    BOX_THICKNESS = 2            # 框线粗细
    TEXT_SCALE = 2               # 文字大小
    
    # 性能配置
    FRAME_DELAY_MS = 50  # 帧间延迟(毫秒)
    
    # YOLO锚点配置
    ANCHOR_POINTS = (1.889, 2.5245, 2.9465, 3.94056, 3.99987, 5.3658, 5.155437, 6.92275, 6.718375, 9.01025)

# 黑色矩形检测配置
class BlackRectConfig:
    # 检测阈值
    BLACK_THRESHOLD_GRAY = [(0, 30)]  # 灰度黑色阈值
    BLACK_THRESHOLD_LAB = [(0, 30, -128, 127, -128, 127)]  # LAB黑色阈值
    BLACK_THRESHOLD_RGB = [(0, 30, 0, 30, 0, 30)]  # RGB黑色阈值

    # 面积过滤
    MIN_AREA = 500  # 最小矩形面积
    MAX_AREA = 50000  # 最大矩形面积

    # 形状过滤
    MIN_RECTANGULARITY = 0.6  # 最小矩形度(0-1)
    MAX_ASPECT_RATIO = 5  # 最大长宽比

    # 绘制配置
    BOX_COLOR = (0, 255, 0)  # 检测框颜色(绿色)
    TEXT_COLOR = (255, 255, 255)  # 文字颜色(白色)
    CENTER_COLOR = (255, 0, 0)  # 中心点颜色(红色)
    DIAGONAL_COLOR = (255, 255, 0)  # 对角线颜色(黄色)
    BOX_THICKNESS = 2  # 框线粗细

    # 显示配置
    RECT_MARGIN = 5  # 矩形边界扩展像素
    INFO_OFFSET_X = 5  # 信息显示X偏移
    INFO_OFFSET_Y = 15  # 信息显示Y偏移

    # 性能配置
    FRAME_DELAY_MS = 50  # 帧间延迟(毫秒)
    GC_INTERVAL = 10  # 垃圾回收间隔(帧数)
