# K210人脸识别 - 健壮兼容版
# 支持多种K210固件版本

# 尝试不同的导入方式
try:
    import sensor, image, time, lcd
    print("基础模块导入成功")
except ImportError as e:
    print("基础模块导入失败: " + str(e))
    raise

# 尝试导入KPU模块
kpu = None
try:
    from maix import KPU as kpu
    print("KPU模块导入成功 (maix.KPU)")
except ImportError:
    try:
        import KPU as kpu
        print("KPU模块导入成功 (KPU)")
    except ImportError:
        try:
            from Maix import KPU as kpu
            print("KPU模块导入成功 (Maix.KPU)")
        except ImportError:
            print("错误: 无法导入KPU模块")
            print("请确认使用的是支持KPU的K210固件")
            raise ImportError("KPU模块不可用")

# 配置参数
MODEL_PATH = "/sd/face_detection.kmodel"
THRESHOLD = 0.7
BOX_COLOR = (0, 255, 0)
TEXT_COLOR = (255, 255, 255)
ANCHOR = (1.889, 2.5245, 2.9465, 3.94056, 3.99987, 5.3658, 5.155437, 6.92275, 6.718375, 9.01025)

def init_hardware():
    """初始化硬件"""
    try:
        print("初始化LCD...")
        lcd.init()
        lcd.clear(lcd.BLACK)
        
        print("初始化摄像头...")
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.set_windowing((320, 240))
        sensor.set_vflip(1)
        sensor.run(1)
        
        print("硬件初始化完成")
        return True
    except Exception as e:
        print("硬件初始化失败: " + str(e))
        return False

def load_model():
    """加载人脸检测模型"""
    try:
        print("加载模型: " + MODEL_PATH)
        task = kpu.load(MODEL_PATH)
        kpu.init_yolo2(task, 0.5, 0.3, 5, ANCHOR)
        print("模型加载成功")
        return task
    except Exception as e:
        print("模型加载失败: " + str(e))
        print("请检查:")
        print("1. SD卡中是否存在模型文件")
        print("2. 模型文件路径是否正确")
        print("3. 模型文件是否损坏")
        return None

def detect_and_draw(task):
    """检测并绘制人脸"""
    frame_count = 0
    
    try:
        while True:
            img = sensor.snapshot()
            if not img:
                continue
                
            # 运行人脸检测
            code = kpu.run_yolo2(task, img)
            
            face_count = 0
            if code:
                for detection in code:
                    confidence = detection.value()
                    if confidence > THRESHOLD:
                        x, y, w, h = detection.x(), detection.y(), detection.w(), detection.h()
                        
                        # 绘制人脸框
                        img.draw_rectangle(x, y, w, h, color=BOX_COLOR, thickness=2)
                        
                        # 显示置信度
                        conf_text = str(int(confidence * 100)) + "%"
                        img.draw_string(x, y-20, conf_text, color=TEXT_COLOR, scale=2)
                        
                        face_count += 1
                        print("人脸 " + str(face_count) + ": (" + str(x) + "," + str(y) + ") 置信度:" + str(int(confidence*100)) + "%")
            
            if face_count > 0:
                print("第" + str(frame_count) + "帧: 检测到" + str(face_count) + "个人脸")
            
            # 显示图像
            lcd.display(img)
            frame_count += 1
            
            # 帧率控制
            time.sleep_ms(50)
            
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print("检测过程出错: " + str(e))

def cleanup(task):
    """清理资源"""
    try:
        if task:
            kpu.deinit(task)
        sensor.shutdown()
        lcd.clear()
        print("资源清理完成")
    except Exception as e:
        print("清理资源时出错: " + str(e))

def main():
    """主函数"""
    print("=== K210人脸识别系统启动 ===")
    
    # 初始化硬件
    if not init_hardware():
        return
    
    # 加载模型
    task = load_model()
    if not task:
        return
    
    print("开始人脸识别...")
    print("按Ctrl+C停止程序")
    
    try:
        # 开始检测
        detect_and_draw(task)
    finally:
        # 清理资源
        cleanup(task)

if __name__ == "__main__":
    main()
