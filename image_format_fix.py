# K210图像格式问题诊断和修复工具
# 专门解决"Image format is not supported"错误

import sensor, image, time, lcd
import gc

def test_pixel_formats():
    """测试不同的像素格式"""
    print("=== 像素格式兼容性测试 ===")
    
    formats = [
        (sensor.RGB565, "RGB565"),
        (sensor.GRAYSCALE, "GRAYSCALE"),
    ]
    
    # 尝试其他格式(如果存在)
    try:
        formats.append((sensor.RGB888, "RGB888"))
    except:
        pass
    
    try:
        formats.append((sensor.YUV422, "YUV422"))
    except:
        pass
    
    working_formats = []
    
    for fmt, name in formats:
        try:
            print("测试{}格式...".format(name))
            sensor.reset()
            sensor.set_pixformat(fmt)
            sensor.set_framesize(sensor.QVGA)
            sensor.skip_frames(time=1000)
            sensor.run(1)
            
            # 尝试获取图像
            img = sensor.snapshot()
            if img:
                print("✅ {}格式工作正常".format(name))
                working_formats.append((fmt, name))
            else:
                print("❌ {}格式无法获取图像".format(name))
                
        except Exception as e:
            print("❌ {}格式失败: {}".format(name, str(e)))
    
    return working_formats

def test_frame_sizes():
    """测试不同的分辨率"""
    print("\n=== 分辨率兼容性测试 ===")
    
    sizes = [
        (sensor.QQVGA, "QQVGA", "160x120"),
        (sensor.QVGA, "QVGA", "320x240"),
        (sensor.VGA, "VGA", "640x480"),
    ]
    
    # 尝试其他分辨率
    try:
        sizes.append((sensor.SVGA, "SVGA", "800x600"))
    except:
        pass
    
    working_sizes = []
    
    for size, name, desc in sizes:
        try:
            print("测试{}分辨率({})...".format(name, desc))
            sensor.reset()
            sensor.set_pixformat(sensor.RGB565)  # 使用基础格式
            sensor.set_framesize(size)
            sensor.skip_frames(time=1000)
            sensor.run(1)
            
            # 尝试获取图像
            img = sensor.snapshot()
            if img:
                actual_w = img.width()
                actual_h = img.height()
                print("✅ {}分辨率工作正常: {}x{}".format(name, actual_w, actual_h))
                working_sizes.append((size, name, actual_w, actual_h))
            else:
                print("❌ {}分辨率无法获取图像".format(name))
                
        except Exception as e:
            print("❌ {}分辨率失败: {}".format(name, str(e)))
    
    return working_sizes

def test_image_operations(img):
    """测试图像操作"""
    print("\n=== 图像操作测试 ===")
    
    operations = [
        ("width()", lambda: img.width()),
        ("height()", lambda: img.height()),
        ("format()", lambda: img.format()),
        ("size()", lambda: img.size()),
    ]
    
    results = {}
    
    for name, op in operations:
        try:
            result = op()
            print("✅ {}: {}".format(name, result))
            results[name] = result
        except Exception as e:
            print("❌ {}: {}".format(name, str(e)))
            results[name] = None
    
    return results

def test_drawing_operations(img):
    """测试绘图操作"""
    print("\n=== 绘图操作测试 ===")
    
    operations = [
        ("draw_string", lambda: img.draw_string(10, 10, "Test", color=(255, 255, 255))),
        ("draw_rectangle", lambda: img.draw_rectangle(50, 50, 100, 100, color=(0, 255, 0))),
        ("draw_circle", lambda: img.draw_circle(100, 100, 30, color=(255, 0, 0))),
    ]
    
    for name, op in operations:
        try:
            op()
            print("✅ {}操作成功".format(name))
        except Exception as e:
            print("❌ {}操作失败: {}".format(name, str(e)))

def find_best_config():
    """找到最佳配置"""
    print("\n=== 寻找最佳配置 ===")
    
    # 测试像素格式
    working_formats = test_pixel_formats()
    if not working_formats:
        print("❌ 没有可用的像素格式!")
        return None
    
    # 测试分辨率
    working_sizes = test_frame_sizes()
    if not working_sizes:
        print("❌ 没有可用的分辨率!")
        return None
    
    # 选择最佳配置
    best_format = working_formats[0]  # 选择第一个可用格式
    best_size = working_sizes[0]      # 选择第一个可用分辨率
    
    print("\n🎯 推荐配置:")
    print("像素格式: {}".format(best_format[1]))
    print("分辨率: {}x{}".format(best_size[2], best_size[3]))
    
    return {
        'format': best_format[0],
        'format_name': best_format[1],
        'size': best_size[0],
        'size_name': best_size[1],
        'width': best_size[2],
        'height': best_size[3]
    }

def test_complete_workflow(config):
    """测试完整工作流程"""
    print("\n=== 完整工作流程测试 ===")
    
    try:
        # 初始化
        print("1. 初始化LCD...")
        lcd.init()
        lcd.clear(lcd.BLACK)
        
        print("2. 配置摄像头...")
        sensor.reset()
        sensor.set_pixformat(config['format'])
        sensor.set_framesize(config['size'])
        sensor.skip_frames(time=2000)
        sensor.run(1)
        
        print("3. 测试图像获取和显示...")
        for i in range(5):
            img = sensor.snapshot()
            if img:
                # 测试图像操作
                img_info = test_image_operations(img)
                
                # 测试绘图
                test_drawing_operations(img)
                
                # 添加测试信息
                img.draw_string(10, 10, "Test {}".format(i+1), color=(255, 255, 255), scale=2)
                img.draw_string(10, 40, "{}x{}".format(img_info.get("width()", "?"), 
                                                      img_info.get("height()", "?")), 
                               color=(0, 255, 0), scale=1)
                
                # 显示图像
                lcd.display(img)
                print("✅ 第{}帧测试成功".format(i+1))
                
                time.sleep_ms(500)
            else:
                print("❌ 第{}帧获取失败".format(i+1))
        
        print("✅ 完整工作流程测试成功!")
        return True
        
    except Exception as e:
        print("❌ 完整工作流程测试失败:", str(e))
        return False

def generate_fixed_code(config):
    """生成修复后的代码"""
    print("\n=== 生成修复代码 ===")
    
    code = '''# 修复后的K210摄像头初始化代码
import sensor, image, time, lcd

def init_camera_fixed():
    """修复后的摄像头初始化"""
    lcd.init()
    lcd.clear(lcd.BLACK)
    
    sensor.reset()
    sensor.set_pixformat(sensor.{})  # 兼容的像素格式
    sensor.set_framesize(sensor.{})   # 兼容的分辨率
    sensor.skip_frames(time=2000)     # 充足的等待时间
    sensor.run(1)
    
    print("摄像头初始化成功: {}格式, {}x{}分辨率")

def safe_snapshot():
    """安全的图像获取"""
    try:
        img = sensor.snapshot()
        if img and img.width() > 0 and img.height() > 0:
            return img
        else:
            return None
    except Exception as e:
        print("图像获取错误:", str(e))
        return None

# 使用示例
init_camera_fixed()
while True:
    img = safe_snapshot()
    if img:
        img.draw_string(10, 10, "Fixed!", color=(0, 255, 0), scale=2)
        lcd.display(img)
    time.sleep_ms(100)
'''.format(config['format_name'], config['size_name'], 
           config['format_name'], config['width'], config['height'])
    
    try:
        with open('camera_fixed.py', 'w') as f:
            f.write(code)
        print("✅ 修复代码已保存到: camera_fixed.py")
    except Exception as e:
        print("❌ 保存代码失败:", str(e))
    
    return code

def main():
    """主函数"""
    print("K210图像格式问题诊断工具")
    print("=" * 40)
    
    try:
        # 找到最佳配置
        config = find_best_config()
        
        if config:
            # 测试完整工作流程
            if test_complete_workflow(config):
                # 生成修复代码
                generate_fixed_code(config)
                
                print("\n🎉 诊断完成!")
                print("推荐使用以下配置:")
                print("- 像素格式: {}".format(config['format_name']))
                print("- 分辨率: {}x{}".format(config['width'], config['height']))
                print("\n修复代码已保存到: camera_fixed.py")
            else:
                print("\n❌ 工作流程测试失败，请检查硬件连接")
        else:
            print("\n❌ 无法找到兼容的配置，请检查硬件")
            
    except Exception as e:
        print("❌ 诊断过程出错:", str(e))
    
    finally:
        # 清理
        try:
            sensor.shutdown()
            lcd.clear()
        except:
            pass

if __name__ == "__main__":
    main()
