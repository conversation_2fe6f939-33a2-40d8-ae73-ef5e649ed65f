# K210错误诊断和修复工具
# 专门解决常见的K210开发错误

import sensor, image, time, lcd
import gc

def test_image_methods():
    """测试图像对象支持的方法"""
    print("=== 图像方法兼容性测试 ===")
    
    try:
        # 初始化硬件
        lcd.init()
        sensor.reset()
        sensor.set_pixformat(sensor.GRAYSCALE)
        sensor.set_framesize(sensor.QVGA)
        sensor.skip_frames(time=1000)
        sensor.run(1)
        
        # 获取测试图像
        img = sensor.snapshot()
        if not img:
            print("❌ 无法获取测试图像")
            return False
        
        print("✅ 图像获取成功: {}x{}".format(img.width(), img.height()))
        
        # 测试各种方法
        methods_to_test = [
            ('to_lab', '转换为LAB色彩空间'),
            ('to_rgb565', '转换为RGB565格式'),
            ('to_grayscale', '转换为灰度格式'),
            ('find_blobs', '查找色块'),
            ('find_circles', '查找圆形'),
            ('find_lines', '查找直线'),
            ('draw_rectangle', '绘制矩形'),
            ('draw_circle', '绘制圆形'),
            ('draw_string', '绘制文字'),
            ('draw_cross', '绘制十字'),
            ('copy', '复制图像'),
            ('save', '保存图像')
        ]
        
        supported_methods = []
        unsupported_methods = []
        
        for method, description in methods_to_test:
            if hasattr(img, method):
                try:
                    # 简单测试方法是否可调用
                    if method == 'to_lab':
                        img.to_lab()
                    elif method == 'find_blobs':
                        img.find_blobs([(0, 30)], pixels_threshold=100)
                    elif method == 'draw_rectangle':
                        img.draw_rectangle(10, 10, 50, 30, color=(255, 0, 0))
                    elif method == 'copy':
                        img.copy()
                    
                    print("✅ 支持: {} - {}".format(method, description))
                    supported_methods.append(method)
                except Exception as e:
                    print("⚠️ 部分支持: {} - {} (错误: {})".format(method, description, str(e)))
                    unsupported_methods.append((method, str(e)))
            else:
                print("❌ 不支持: {} - {}".format(method, description))
                unsupported_methods.append((method, "方法不存在"))
        
        print("\n总结:")
        print("- 支持的方法: {}".format(len(supported_methods)))
        print("- 不支持的方法: {}".format(len(unsupported_methods)))
        
        return supported_methods, unsupported_methods
        
    except Exception as e:
        print("❌ 图像方法测试失败:", str(e))
        return [], []

def fix_to_lab_error():
    """修复'Image' object has no attribute 'to_lab'错误"""
    print("\n=== 修复to_lab错误 ===")
    
    print("错误原因:")
    print("- 某些K210固件版本不支持to_lab()方法")
    print("- CanMV和MaixPy固件差异")
    print("- 固件版本过旧")
    
    print("\n解决方案:")
    print("1. 使用兼容版本:")
    print("   exec(open('k210_black_rect_compatible.py').read())")
    
    print("\n2. 修改代码避免使用to_lab:")
    print("   # 原代码:")
    print("   # img_lab = img.to_lab()")
    print("   # blobs = img_lab.find_blobs(threshold)")
    print("   ")
    print("   # 修复后:")
    print("   # 直接使用灰度阈值")
    print("   blobs = img.find_blobs([(0, 30)], pixels_threshold=100)")
    
    print("\n3. 使用条件检查:")
    print("""
if hasattr(img, 'to_lab'):
    img_lab = img.to_lab()
    # 使用LAB检测
else:
    # 使用灰度检测
    blobs = img.find_blobs([(0, 30)])
""")

def fix_image_format_error():
    """修复图像格式错误"""
    print("\n=== 修复图像格式错误 ===")
    
    print("常见错误:")
    print("- Image format is not supported")
    print("- 摄像头初始化失败")
    print("- 像素格式不兼容")
    
    print("\n解决方案:")
    print("1. 使用格式修复工具:")
    print("   exec(open('k210_simple_camera.py').read())")
    print("   exec(open('image_format_fix.py').read())")
    
    print("\n2. 手动设置兼容格式:")
    print("""
# 优先使用灰度模式
try:
    sensor.set_pixformat(sensor.GRAYSCALE)
except:
    sensor.set_pixformat(sensor.RGB565)

# 降级分辨率
try:
    sensor.set_framesize(sensor.QVGA)
except:
    sensor.set_framesize(sensor.QQVGA)
""")

def fix_import_errors():
    """修复导入错误"""
    print("\n=== 修复导入错误 ===")
    
    print("常见错误:")
    print("- ImportError: no module named 'KPU'")
    print("- 模块导入失败")
    print("- 固件版本不匹配")
    
    print("\n解决方案:")
    print("1. CanMV固件用户:")
    print("   from maix import KPU  # 不是 import KPU")
    print("   exec(open('k210_canmv_face.py').read())")
    
    print("\n2. MaixPy固件用户:")
    print("   import KPU as kpu")
    print("   exec(open('k210_face_robust.py').read())")
    
    print("\n3. 诊断固件类型:")
    print("   exec(open('canmv_diagnostic.py').read())")

def generate_fixed_code():
    """生成修复后的代码"""
    print("\n=== 生成修复后的代码 ===")
    
    fixed_code = '''# K210黑色矩形识别 - 错误修复版
import sensor, image, time, lcd

def safe_init():
    """安全的硬件初始化"""
    lcd.init()
    sensor.reset()
    
    # 优先使用兼容性最好的格式
    try:
        sensor.set_pixformat(sensor.GRAYSCALE)
        print("使用GRAYSCALE格式")
    except:
        sensor.set_pixformat(sensor.RGB565)
        print("使用RGB565格式")
    
    try:
        sensor.set_framesize(sensor.QVGA)
    except:
        sensor.set_framesize(sensor.QQVGA)
    
    sensor.set_vflip(1)
    sensor.skip_frames(time=2000)
    sensor.run(1)

def safe_detect(img):
    """安全的黑色矩形检测"""
    try:
        # 使用灰度阈值(兼容性最好)
        threshold = [(0, 30)]
        blobs = img.find_blobs(threshold, pixels_threshold=300, merge=True)
        
        rectangles = []
        for blob in blobs:
            # 简单的矩形过滤
            area = blob.area()
            w, h = blob.w(), blob.h()
            rect_ratio = area / (w * h)
            
            if area > 200 and rect_ratio > 0.5:
                rectangles.append(blob)
        
        return rectangles
    except Exception as e:
        print("检测错误:", str(e))
        return []

def main():
    """主函数"""
    safe_init()
    clock = time.clock()
    
    while True:
        clock.tick()
        img = sensor.snapshot()
        
        rectangles = safe_detect(img)
        
        # 绘制结果
        for i, rect in enumerate(rectangles):
            img.draw_rectangle(rect.rect(), color=(0,255,0), thickness=2)
            img.draw_string(rect.x(), rect.y()-15, "矩形{}".format(i+1), 
                          color=(255,255,255), scale=1)
        
        # 显示状态
        fps = clock.fps()
        img.draw_string(5, 5, "FPS:{:.1f}".format(fps), color=(255,255,255), scale=2)
        img.draw_string(5, 25, "矩形:{}".format(len(rectangles)), color=(0,255,255), scale=2)
        
        lcd.display(img)

if __name__ == "__main__":
    main()
'''
    
    # 保存修复后的代码
    try:
        with open('k210_fixed_black_rect.py', 'w') as f:
            f.write(fixed_code)
        print("✅ 修复后的代码已保存到: k210_fixed_black_rect.py")
        print("使用方法: exec(open('k210_fixed_black_rect.py').read())")
    except Exception as e:
        print("❌ 保存代码失败:", str(e))
        print("请手动复制以下代码:")
        print(fixed_code)

def run_comprehensive_test():
    """运行综合测试"""
    print("K210错误诊断和修复工具")
    print("=" * 40)
    
    try:
        # 测试图像方法
        supported, unsupported = test_image_methods()
        
        # 检查常见问题
        if 'to_lab' not in supported:
            fix_to_lab_error()
        
        # 生成修复代码
        generate_fixed_code()
        
        # 显示其他修复方案
        fix_image_format_error()
        fix_import_errors()
        
        print("\n" + "=" * 40)
        print("修复建议总结:")
        print("1. 优先使用: k210_black_rect_compatible.py")
        print("2. 图像格式问题: k210_simple_camera.py")
        print("3. 导入错误: canmv_diagnostic.py")
        print("4. 自动生成: k210_fixed_black_rect.py")
        
    except Exception as e:
        print("❌ 综合测试失败:", str(e))

def cleanup():
    """清理资源"""
    try:
        sensor.shutdown()
        lcd.clear()
    except:
        pass

def main():
    """主函数"""
    try:
        run_comprehensive_test()
    except Exception as e:
        print("❌ 程序运行出错:", str(e))
    finally:
        cleanup()

if __name__ == "__main__":
    main()
