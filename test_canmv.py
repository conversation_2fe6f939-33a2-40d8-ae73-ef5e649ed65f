# CanMV K210快速测试脚本
# 验证CanMV环境是否正常工作

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        import sensor, image, time, lcd
        print("✓ 基础模块导入成功")
    except ImportError as e:
        print("✗ 基础模块导入失败:", str(e))
        return False
    
    try:
        from maix import KPU
        print("✓ KPU模块导入成功")
        return True
    except ImportError as e:
        print("✗ KPU模块导入失败:", str(e))
        return False

def test_hardware():
    """测试硬件功能"""
    print("\n=== 测试硬件功能 ===")
    
    try:
        import sensor, lcd
        
        # 测试LCD
        lcd.init()
        lcd.clear(lcd.BLACK)
        print("✓ LCD测试通过")
        
        # 测试摄像头
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.run(1)
        
        # 获取图像
        img = sensor.snapshot()
        if img:
            print("✓ 摄像头测试通过 - 图像尺寸: {}x{}".format(img.width(), img.height()))
            lcd.display(img)
            print("✓ 图像显示测试通过")
            return True
        else:
            print("✗ 无法获取图像")
            return False
            
    except Exception as e:
        print("✗ 硬件测试失败:", str(e))
        return False

def test_kpu():
    """测试KPU功能"""
    print("\n=== 测试KPU功能 ===")
    
    try:
        from maix import KPU
        
        # 创建KPU实例
        kpu = KPU()
        print("✓ KPU实例创建成功")
        
        # 检查关键方法
        methods = ['load_kmodel', 'init_yolo2', 'run_with_output', 'regionlayer_yolo2']
        for method in methods:
            if hasattr(kpu, method):
                print("✓ KPU.{} 方法可用".format(method))
            else:
                print("✗ KPU.{} 方法不可用".format(method))
                return False
        
        return True
        
    except Exception as e:
        print("✗ KPU测试失败:", str(e))
        return False

def test_sd_card():
    """测试SD卡"""
    print("\n=== 测试SD卡 ===")
    
    try:
        import os
        
        files = os.listdir("/sd")
        print("✓ SD卡可访问，文件数量:", len(files))
        
        # 检查模型文件
        model_files = ["face_detect_320x240.kmodel", "face_detection.kmodel"]
        found = False
        for model in model_files:
            if model in files:
                print("✓ 找到模型文件:", model)
                found = True
                break
        
        if not found:
            print("⚠ 未找到模型文件，请确保SD卡中有人脸检测模型")
        
        return True
        
    except Exception as e:
        print("✗ SD卡测试失败:", str(e))
        return False

def run_live_test():
    """运行实时测试"""
    print("\n=== 运行实时测试 ===")
    print("将显示5秒实时图像...")
    
    try:
        import sensor, lcd, time
        
        # 初始化
        lcd.init()
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.run(1)
        
        # 显示5秒实时图像
        start_time = time.ticks_ms()
        frame_count = 0
        
        while time.ticks_diff(time.ticks_ms(), start_time) < 5000:  # 5秒
            img = sensor.snapshot()
            if img:
                # 在图像上绘制信息
                img.draw_string(10, 10, "CanMV Test", color=(255, 255, 255), scale=2)
                img.draw_string(10, 40, "Frame: {}".format(frame_count), color=(0, 255, 0), scale=1)
                lcd.display(img)
                frame_count += 1
            
            time.sleep_ms(100)  # 100ms延迟
        
        print("✓ 实时测试完成，共处理{}帧".format(frame_count))
        return True
        
    except Exception as e:
        print("✗ 实时测试失败:", str(e))
        return False

def main():
    """主测试函数"""
    print("CanMV K210快速测试")
    print("=" * 30)
    
    tests = [
        ("模块导入", test_imports),
        ("硬件功能", test_hardware), 
        ("KPU功能", test_kpu),
        ("SD卡", test_sd_card),
        ("实时测试", run_live_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print("\n正在执行: {}...".format(name))
        if test_func():
            passed += 1
            print("✓ {} 测试通过".format(name))
        else:
            print("✗ {} 测试失败".format(name))
    
    print("\n" + "=" * 30)
    print("测试结果: {}/{} 通过 ({:.1f}%)".format(passed, total, passed*100/total))
    
    if passed == total:
        print("🎉 所有测试通过！CanMV环境正常")
        print("可以运行人脸识别程序:")
        print("exec(open('k210_canmv_face.py').read())")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        print("建议运行完整诊断:")
        print("exec(open('canmv_diagnostic.py').read())")

if __name__ == "__main__":
    main()
