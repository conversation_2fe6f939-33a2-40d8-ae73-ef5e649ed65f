# K210黑色矩形识别 - 兼容性修复版
# 解决'Image' object has no attribute 'to_lab'错误

import sensor, image, time, lcd
import gc

# 兼容性配置
GRAY_BLACK_THRESHOLD = [(0, 30)]  # 灰度黑色阈值
RGB_BLACK_THRESHOLD = [(0, 30, 0, 30, 0, 30)]  # RGB黑色阈值
MIN_AREA = 500  # 最小矩形面积
MAX_AREA = 50000  # 最大矩形面积
MIN_RECTANGULARITY = 0.6  # 最小矩形度
BOX_COLOR = (0, 255, 0)  # 检测框颜色
TEXT_COLOR = (255, 255, 255)  # 文字颜色

def init_hardware():
    """兼容性硬件初始化"""
    print("初始化硬件(兼容模式)...")
    lcd.init()
    lcd.clear(lcd.BLACK)
    
    sensor.reset()
    
    # 优先使用灰度模式(兼容性最好)
    try:
        sensor.set_pixformat(sensor.GRAYSCALE)
        print("✅ 使用GRAYSCALE格式(兼容模式)")
        return 'grayscale'
    except:
        try:
            sensor.set_pixformat(sensor.RGB565)
            print("✅ 使用RGB565格式")
            return 'rgb'
        except Exception as e:
            print("❌ 像素格式设置失败:", str(e))
            return None
    
    try:
        sensor.set_framesize(sensor.QVGA)
        print("✅ 使用QVGA分辨率")
    except:
        sensor.set_framesize(sensor.QQVGA)
        print("✅ 使用QQVGA分辨率")
    
    sensor.set_vflip(1)
    sensor.skip_frames(time=2000)
    sensor.run(1)
    
    print("✅ 硬件初始化完成")

def find_black_rectangles_gray(img):
    """灰度模式黑色矩形检测"""
    rectangles = []
    
    try:
        # 使用灰度阈值
        blobs = img.find_blobs(GRAY_BLACK_THRESHOLD, 
                               pixels_threshold=MIN_AREA, 
                               merge=True)
        
        for blob in blobs:
            # 面积过滤
            if not (MIN_AREA <= blob.area() <= MAX_AREA):
                continue
            
            # 矩形度过滤
            rectangularity = blob.area() / (blob.w() * blob.h())
            if rectangularity < MIN_RECTANGULARITY:
                continue
            
            # 长宽比过滤
            aspect_ratio = max(blob.w(), blob.h()) / min(blob.w(), blob.h())
            if aspect_ratio > 5:
                continue
            
            rectangles.append({
                'x': blob.x(), 'y': blob.y(), 
                'w': blob.w(), 'h': blob.h(),
                'area': blob.area(),
                'rectangularity': rectangularity,
                'aspect_ratio': aspect_ratio,
                'cx': blob.cx(), 'cy': blob.cy()
            })
        
        # 按面积排序
        rectangles.sort(key=lambda r: r['area'], reverse=True)
        
    except Exception as e:
        print("❌ 灰度检测错误:", str(e))
    
    return rectangles

def find_black_rectangles_rgb(img):
    """RGB模式黑色矩形检测(无需to_lab)"""
    rectangles = []
    
    try:
        # 直接使用RGB阈值
        blobs = img.find_blobs(RGB_BLACK_THRESHOLD, 
                               pixels_threshold=MIN_AREA, 
                               merge=True)
        
        for blob in blobs:
            # 面积过滤
            if not (MIN_AREA <= blob.area() <= MAX_AREA):
                continue
            
            # 矩形度过滤
            rectangularity = blob.area() / (blob.w() * blob.h())
            if rectangularity < MIN_RECTANGULARITY:
                continue
            
            # 长宽比过滤
            aspect_ratio = max(blob.w(), blob.h()) / min(blob.w(), blob.h())
            if aspect_ratio > 5:
                continue
            
            rectangles.append({
                'x': blob.x(), 'y': blob.y(), 
                'w': blob.w(), 'h': blob.h(),
                'area': blob.area(),
                'rectangularity': rectangularity,
                'aspect_ratio': aspect_ratio,
                'cx': blob.cx(), 'cy': blob.cy()
            })
        
        # 按面积排序
        rectangles.sort(key=lambda r: r['area'], reverse=True)
        
    except Exception as e:
        print("❌ RGB检测错误:", str(e))
    
    return rectangles

def test_image_methods(img):
    """测试图像对象支持的方法"""
    print("测试图像方法支持情况:")
    
    methods_to_test = [
        'to_lab', 'to_rgb565', 'to_grayscale', 
        'find_blobs', 'draw_rectangle', 'draw_string'
    ]
    
    supported_methods = []
    
    for method in methods_to_test:
        if hasattr(img, method):
            print("✅ 支持方法:", method)
            supported_methods.append(method)
        else:
            print("❌ 不支持方法:", method)
    
    return supported_methods

def draw_rectangle_info(img, rect, index):
    """绘制矩形信息"""
    try:
        x, y, w, h = rect['x'], rect['y'], rect['w'], rect['h']
        cx, cy = rect['cx'], rect['cy']
        
        # 绘制检测框
        img.draw_rectangle(x, y, w, h, color=BOX_COLOR, thickness=2)
        
        # 绘制中心点
        img.draw_cross(cx, cy, color=(255, 0, 0), size=8, thickness=2)
        
        # 显示基本信息
        info_y = y - 25 if y > 25 else y + h + 5
        img.draw_string(x, info_y, "矩形{}".format(index + 1), color=TEXT_COLOR, scale=1)
        img.draw_string(x, info_y + 12, "{}x{}".format(w, h), color=TEXT_COLOR, scale=1)
        
        # 显示面积
        img.draw_string(x + w + 5, y, "面积:{}".format(rect['area']), color=(255, 255, 0), scale=1)
        
        # 在矩形中心显示编号
        img.draw_string(cx-5, cy-5, str(index+1), color=TEXT_COLOR, scale=2)
        
    except Exception as e:
        print("⚠️ 绘制错误:", str(e))

def detect_black_rectangles():
    """兼容性黑色矩形检测主循环"""
    # 初始化硬件并获取模式
    pixel_format = init_hardware()
    if not pixel_format:
        print("❌ 硬件初始化失败")
        return
    
    clock = time.clock()
    frame_count = 0
    total_rectangles = 0
    
    print("开始兼容性黑色矩形检测...")
    print("像素格式:", pixel_format)
    print("检测参数:")
    print("- 最小面积:", MIN_AREA)
    print("- 最大面积:", MAX_AREA)
    print("- 最小矩形度:", MIN_RECTANGULARITY)
    print("按Ctrl+C停止程序")
    
    # 选择检测函数
    if pixel_format == 'grayscale':
        detect_func = find_black_rectangles_gray
        print("使用灰度检测模式")
    else:
        detect_func = find_black_rectangles_rgb
        print("使用RGB检测模式")
    
    try:
        while True:
            clock.tick()
            
            # 安全获取图像
            try:
                img = sensor.snapshot()
                if not img:
                    continue
            except Exception as e:
                print("❌ 图像获取错误:", str(e))
                continue
            
            # 首次运行时测试图像方法
            if frame_count == 0:
                supported_methods = test_image_methods(img)
                print("支持的方法数:", len(supported_methods))
            
            # 执行检测
            rectangles = detect_func(img)
            
            # 绘制检测结果
            rect_count = len(rectangles)
            if rect_count > 0:
                for i, rect in enumerate(rectangles):
                    draw_rectangle_info(img, rect, i)
                
                total_rectangles += rect_count
                
                # 打印检测信息
                print("第{}帧: 检测到{}个黑色矩形".format(frame_count, rect_count))
                for i, rect in enumerate(rectangles):
                    print("  矩形{}: 位置({},{}) 大小{}x{} 面积:{}".format(
                        i + 1, rect['x'], rect['y'], rect['w'], rect['h'], rect['area']))
            
            # 显示状态信息
            fps = clock.fps()
            img.draw_string(5, 5, "FPS:{:.1f}".format(fps), color=TEXT_COLOR, scale=2)
            img.draw_string(5, 25, "帧:{}".format(frame_count), color=TEXT_COLOR, scale=1)
            img.draw_string(5, 40, "矩形:{}".format(rect_count), color=(0, 255, 255), scale=2)
            img.draw_string(5, 60, "模式:{}".format(pixel_format), color=(255, 255, 0), scale=1)
            
            # 显示统计信息
            if frame_count > 0:
                avg_rects = total_rectangles / frame_count
                img.draw_string(5, 80, "平均:{:.1f}".format(avg_rects), color=(255, 255, 0), scale=1)
            
            # 显示检测状态
            if rect_count > 0:
                img.draw_string(200, 5, "检测中", color=(0, 255, 0), scale=2)
            else:
                img.draw_string(200, 5, "搜索中", color=(255, 255, 0), scale=2)
            
            # 显示图像
            try:
                lcd.display(img)
            except Exception as e:
                print("❌ 图像显示错误:", str(e))
            
            # 垃圾回收
            if frame_count % 10 == 0:
                gc.collect()
            
            frame_count += 1
            time.sleep_ms(50)
            
    except KeyboardInterrupt:
        print("程序被用户中断")
        print("统计信息:")
        print("- 总帧数:", frame_count)
        print("- 总检测矩形数:", total_rectangles)
        if frame_count > 0:
            print("- 平均每帧矩形数:", total_rectangles / frame_count)
    except Exception as e:
        print("❌ 检测过程出错:", str(e))

def test_compatibility():
    """兼容性测试"""
    print("=== K210兼容性测试 ===")
    
    try:
        # 初始化硬件
        pixel_format = init_hardware()
        if not pixel_format:
            return False
        
        # 获取测试图像
        img = sensor.snapshot()
        if not img:
            print("❌ 无法获取测试图像")
            return False
        
        print("✅ 图像获取成功: {}x{}".format(img.width(), img.height()))
        
        # 测试图像方法
        supported_methods = test_image_methods(img)
        
        # 测试检测功能
        if pixel_format == 'grayscale':
            rectangles = find_black_rectangles_gray(img)
        else:
            rectangles = find_black_rectangles_rgb(img)
        
        print("✅ 检测功能测试通过，找到{}个矩形".format(len(rectangles)))
        
        # 测试绘制功能
        for i, rect in enumerate(rectangles):
            draw_rectangle_info(img, rect, i)
        
        img.draw_string(10, 10, "兼容性测试", color=(255, 255, 255), scale=2)
        lcd.display(img)
        
        print("✅ 兼容性测试通过")
        return True
        
    except Exception as e:
        print("❌ 兼容性测试失败:", str(e))
        return False

def show_usage():
    """显示使用说明"""
    print("K210黑色矩形识别 - 兼容性修复版")
    print("=" * 40)
    print("解决问题:")
    print("- 'Image' object has no attribute 'to_lab'")
    print("- 不同固件版本的兼容性问题")
    print("- 像素格式不支持问题")
    
    print("\n兼容性特性:")
    print("- 自动检测支持的像素格式")
    print("- 优先使用灰度模式(兼容性最好)")
    print("- 避免使用to_lab()方法")
    print("- 完善的错误处理")
    
    print("\n使用方法:")
    print("1. 直接运行: exec(open('k210_black_rect_compatible.py').read())")
    print("2. 先测试兼容性: test_compatibility()")
    print("3. 然后运行检测: detect_black_rectangles()")

def cleanup():
    """清理资源"""
    try:
        sensor.shutdown()
        lcd.clear()
        print("✅ 资源清理完成")
    except Exception as e:
        print("清理资源时出错:", str(e))

def main():
    """主函数"""
    show_usage()
    
    try:
        # 先进行兼容性测试
        print("\n进行兼容性测试...")
        if test_compatibility():
            time.sleep_ms(2000)
            
            # 开始检测
            detect_black_rectangles()
        else:
            print("❌ 兼容性测试失败，请检查硬件和固件")
        
    except Exception as e:
        print("❌ 程序运行出错:", str(e))
    finally:
        cleanup()

if __name__ == "__main__":
    main()
