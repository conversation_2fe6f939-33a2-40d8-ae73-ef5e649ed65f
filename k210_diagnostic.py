# K210环境诊断脚本
# 检查K210开发板的模块和硬件状态

import sys

def check_basic_modules():
    """检查基础模块"""
    print("=== 检查基础模块 ===")
    
    modules = ['sensor', 'image', 'time', 'lcd']
    for module in modules:
        try:
            __import__(module)
            print("✓ " + module + " 模块可用")
        except ImportError:
            print("✗ " + module + " 模块不可用")
            return False
    return True

def check_kpu_module():
    """检查KPU模块"""
    print("\n=== 检查KPU模块 ===")
    
    # 尝试不同的导入方式
    import_methods = [
        ("from maix import KPU", "maix.KPU"),
        ("import KPU", "KPU"),
        ("from Maix import KPU", "Maix.KPU")
    ]
    
    for method, name in import_methods:
        try:
            exec(method)
            print("✓ KPU模块可用: " + name)
            return True
        except ImportError:
            print("✗ " + name + " 导入失败")
    
    print("✗ 所有KPU导入方式都失败")
    return False

def check_hardware():
    """检查硬件状态"""
    print("\n=== 检查硬件状态 ===")
    
    try:
        import sensor, lcd
        
        # 检查LCD
        try:
            lcd.init()
            print("✓ LCD初始化成功")
            lcd.clear(lcd.BLACK)
            print("✓ LCD清屏成功")
        except Exception as e:
            print("✗ LCD错误: " + str(e))
            return False
        
        # 检查摄像头
        try:
            sensor.reset()
            print("✓ 摄像头重置成功")
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            print("✓ 摄像头配置成功")
            sensor.run(1)
            print("✓ 摄像头启动成功")
        except Exception as e:
            print("✗ 摄像头错误: " + str(e))
            return False
            
        return True
        
    except ImportError as e:
        print("✗ 硬件模块导入失败: " + str(e))
        return False

def check_sd_card():
    """检查SD卡"""
    print("\n=== 检查SD卡 ===")
    
    try:
        import os
        
        # 检查SD卡挂载
        try:
            files = os.listdir("/sd")
            print("✓ SD卡可访问")
            print("SD卡文件: " + str(files))
            
            # 检查模型文件
            model_file = "/sd/face_detection.kmodel"
            if "face_detection.kmodel" in files:
                print("✓ 找到人脸检测模型文件")
                
                # 检查文件大小
                try:
                    stat = os.stat(model_file)
                    size = stat[6]  # 文件大小
                    print("模型文件大小: " + str(size) + " 字节")
                    if size > 0:
                        print("✓ 模型文件不为空")
                    else:
                        print("✗ 模型文件为空")
                except:
                    print("? 无法获取模型文件信息")
            else:
                print("✗ 未找到人脸检测模型文件")
                print("请将 face_detection.kmodel 放入SD卡根目录")
                
        except Exception as e:
            print("✗ SD卡访问失败: " + str(e))
            return False
            
        return True
        
    except ImportError:
        print("✗ os模块不可用")
        return False

def check_memory():
    """检查内存状态"""
    print("\n=== 检查内存状态 ===")
    
    try:
        import gc
        
        # 执行垃圾回收
        gc.collect()
        
        # 获取内存信息
        free_mem = gc.mem_free()
        alloc_mem = gc.mem_alloc()
        total_mem = free_mem + alloc_mem
        
        print("总内存: " + str(total_mem) + " 字节")
        print("已用内存: " + str(alloc_mem) + " 字节")
        print("可用内存: " + str(free_mem) + " 字节")
        print("内存使用率: " + str(int(alloc_mem * 100 / total_mem)) + "%")
        
        if free_mem > 100000:  # 100KB
            print("✓ 可用内存充足")
            return True
        else:
            print("⚠ 可用内存较少，可能影响模型加载")
            return False
            
    except ImportError:
        print("✗ gc模块不可用")
        return False

def print_system_info():
    """打印系统信息"""
    print("\n=== 系统信息 ===")
    
    try:
        print("Python版本: " + sys.version)
        print("平台: " + sys.platform)
        
        # 尝试获取更多信息
        try:
            import machine
            print("机器信息可用")
        except ImportError:
            print("machine模块不可用")
            
    except Exception as e:
        print("获取系统信息失败: " + str(e))

def main():
    """主诊断函数"""
    print("K210开发板环境诊断")
    print("=" * 30)
    
    results = []
    
    # 执行各项检查
    results.append(("基础模块", check_basic_modules()))
    results.append(("KPU模块", check_kpu_module()))
    results.append(("硬件状态", check_hardware()))
    results.append(("SD卡", check_sd_card()))
    results.append(("内存状态", check_memory()))
    
    # 打印系统信息
    print_system_info()
    
    # 总结结果
    print("\n=== 诊断结果总结 ===")
    all_passed = True
    for name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(name + ": " + status)
        if not result:
            all_passed = False
    
    print("\n" + "=" * 30)
    if all_passed:
        print("✓ 所有检查通过，可以运行人脸识别程序")
    else:
        print("✗ 存在问题，请根据上述信息进行修复")
        print("\n常见解决方案:")
        print("1. 确认使用支持KPU的K210固件")
        print("2. 检查硬件连接")
        print("3. 确认SD卡正确插入并包含模型文件")
        print("4. 重启开发板后重试")

if __name__ == "__main__":
    main()
