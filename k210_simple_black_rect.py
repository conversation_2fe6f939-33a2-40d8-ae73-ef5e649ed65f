# K210简化黑色矩形识别 - 最少代码实现
# 专注核心功能，代码行数最少

import sensor, image, time, lcd

# 配置参数
BLACK_THRESHOLD = [(0, 30)]  # 灰度黑色阈值
MIN_AREA = 300  # 最小面积
BOX_COLOR = (0, 255, 0)  # 绿色框

def init():
    """初始化"""
    lcd.init()
    sensor.reset()
    sensor.set_pixformat(sensor.GRAYSCALE)  # 灰度模式更简单
    sensor.set_framesize(sensor.QVGA)
    sensor.skip_frames(time=1000)
    sensor.run(1)
    print("✅ 初始化完成")

def find_rectangles(img):
    """查找黑色矩形"""
    blobs = img.find_blobs(BLACK_THRESHOLD, pixels_threshold=MIN_AREA, merge=True)
    rectangles = []
    
    for blob in blobs:
        # 计算矩形度
        rect_ratio = blob.area() / (blob.w() * blob.h())
        if rect_ratio > 0.6:  # 过滤非矩形形状
            rectangles.append(blob)
    
    return rectangles

def draw_results(img, rectangles):
    """绘制结果"""
    for i, rect in enumerate(rectangles):
        # 绘制矩形框
        img.draw_rectangle(rect.rect(), color=BOX_COLOR, thickness=2)
        # 绘制中心点
        img.draw_cross(rect.cx(), rect.cy(), color=BOX_COLOR, size=8)
        # 显示编号
        img.draw_string(rect.x(), rect.y()-15, "矩形{}".format(i+1), color=255, scale=1)
        # 显示尺寸
        img.draw_string(rect.x(), rect.y()+rect.h()+5, "{}x{}".format(rect.w(), rect.h()), color=255, scale=1)

def main():
    """主函数"""
    init()
    clock = time.clock()
    frame = 0
    
    print("开始黑色矩形检测...")
    print("按Ctrl+C停止")
    
    try:
        while True:
            clock.tick()
            img = sensor.snapshot()
            
            # 查找矩形
            rectangles = find_rectangles(img)
            
            # 绘制结果
            draw_results(img, rectangles)
            
            # 显示状态
            fps = clock.fps()
            img.draw_string(5, 5, "FPS:{:.1f}".format(fps), color=255, scale=2)
            img.draw_string(5, 25, "矩形:{}".format(len(rectangles)), color=255, scale=2)
            img.draw_string(5, 45, "帧:{}".format(frame), color=255, scale=1)
            
            # 打印检测结果
            if rectangles:
                print("第{}帧: 检测到{}个矩形".format(frame, len(rectangles)))
                for i, rect in enumerate(rectangles):
                    print("  矩形{}: ({},{}) {}x{} 面积:{}".format(
                        i+1, rect.x(), rect.y(), rect.w(), rect.h(), rect.area()))
            
            lcd.display(img)
            frame += 1
            
    except KeyboardInterrupt:
        print("程序结束")
    except Exception as e:
        print("错误:", str(e))

if __name__ == "__main__":
    main()
