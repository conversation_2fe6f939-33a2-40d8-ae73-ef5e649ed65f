# K210黑色矩形识别测试脚本
# 测试各种版本的黑色矩形识别功能

import sensor, image, time, lcd
import gc

def test_hardware():
    """测试硬件初始化"""
    print("=== 硬件测试 ===")
    
    try:
        # 初始化LCD
        lcd.init()
        lcd.clear(lcd.BLACK)
        print("✅ LCD初始化成功")
        
        # 初始化摄像头
        sensor.reset()
        
        # 测试像素格式
        formats_to_test = [
            (sensor.RGB565, "RGB565"),
            (sensor.GRAYSCALE, "GRAYSCALE")
        ]
        
        working_format = None
        for fmt, name in formats_to_test:
            try:
                sensor.set_pixformat(fmt)
                print("✅ {}格式支持".format(name))
                working_format = (fmt, name)
                break
            except Exception as e:
                print("❌ {}格式不支持: {}".format(name, str(e)))
        
        if not working_format:
            print("❌ 没有可用的像素格式")
            return False
        
        # 测试分辨率
        resolutions_to_test = [
            (sensor.QVGA, "QVGA(320x240)"),
            (sensor.QQVGA, "QQVGA(160x120)")
        ]
        
        working_resolution = None
        for res, name in resolutions_to_test:
            try:
                sensor.set_framesize(res)
                print("✅ {}分辨率支持".format(name))
                working_resolution = (res, name)
                break
            except Exception as e:
                print("❌ {}分辨率不支持: {}".format(name, str(e)))
        
        if not working_resolution:
            print("❌ 没有可用的分辨率")
            return False
        
        sensor.set_vflip(1)
        sensor.skip_frames(time=2000)
        sensor.run(1)
        
        print("✅ 硬件测试通过")
        print("- 使用格式:", working_format[1])
        print("- 使用分辨率:", working_resolution[1])
        
        return True
        
    except Exception as e:
        print("❌ 硬件测试失败:", str(e))
        return False

def test_image_capture():
    """测试图像捕获"""
    print("\n=== 图像捕获测试 ===")
    
    try:
        for i in range(5):
            img = sensor.snapshot()
            if img:
                w, h = img.width(), img.height()
                print("✅ 第{}帧: {}x{}".format(i+1, w, h))
                
                # 显示测试图像
                img.draw_string(10, 10, "测试图像 {}".format(i+1), color=(255, 255, 255), scale=2)
                lcd.display(img)
                time.sleep_ms(500)
            else:
                print("❌ 第{}帧: 图像为空".format(i+1))
                return False
        
        print("✅ 图像捕获测试通过")
        return True
        
    except Exception as e:
        print("❌ 图像捕获测试失败:", str(e))
        return False

def test_black_detection():
    """测试黑色检测"""
    print("\n=== 黑色检测测试 ===")
    
    try:
        # 测试不同的黑色阈值
        thresholds_to_test = [
            ([(0, 30)], "宽松阈值"),
            ([(0, 20)], "中等阈值"),
            ([(0, 15)], "严格阈值")
        ]
        
        img = sensor.snapshot()
        if not img:
            print("❌ 无法获取测试图像")
            return False
        
        for threshold, name in thresholds_to_test:
            try:
                blobs = img.find_blobs(threshold, pixels_threshold=100)
                print("✅ {}: 找到{}个黑色区域".format(name, len(blobs)))
                
                # 在图像上标记黑色区域
                test_img = img.copy()
                for blob in blobs:
                    test_img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
                
                test_img.draw_string(10, 10, name, color=(255, 255, 255), scale=2)
                lcd.display(test_img)
                time.sleep_ms(1000)
                
            except Exception as e:
                print("❌ {}: 检测失败 - {}".format(name, str(e)))
        
        print("✅ 黑色检测测试通过")
        return True
        
    except Exception as e:
        print("❌ 黑色检测测试失败:", str(e))
        return False

def test_rectangle_filtering():
    """测试矩形过滤"""
    print("\n=== 矩形过滤测试 ===")
    
    try:
        img = sensor.snapshot()
        if not img:
            print("❌ 无法获取测试图像")
            return False
        
        # 查找黑色区域
        blobs = img.find_blobs([(0, 30)], pixels_threshold=200)
        print("找到{}个黑色区域".format(len(blobs)))
        
        rectangles = []
        for blob in blobs:
            # 计算矩形度
            rectangularity = blob.area() / (blob.w() * blob.h())
            
            # 计算长宽比
            aspect_ratio = max(blob.w(), blob.h()) / min(blob.w(), blob.h())
            
            print("区域: 面积={} 矩形度={:.2f} 长宽比={:.1f}".format(
                blob.area(), rectangularity, aspect_ratio))
            
            # 过滤条件
            if (blob.area() >= 300 and 
                rectangularity >= 0.6 and 
                aspect_ratio <= 5):
                rectangles.append(blob)
                print("  ✅ 通过过滤")
            else:
                print("  ❌ 未通过过滤")
        
        print("过滤后剩余{}个矩形".format(len(rectangles)))
        
        # 绘制过滤结果
        for i, rect in enumerate(rectangles):
            img.draw_rectangle(rect.rect(), color=(0, 255, 0), thickness=2)
            img.draw_string(rect.x(), rect.y()-15, "矩形{}".format(i+1), color=(255, 255, 255), scale=1)
        
        img.draw_string(10, 10, "过滤结果", color=(255, 255, 255), scale=2)
        lcd.display(img)
        
        print("✅ 矩形过滤测试通过")
        return True
        
    except Exception as e:
        print("❌ 矩形过滤测试失败:", str(e))
        return False

def test_performance():
    """测试性能"""
    print("\n=== 性能测试 ===")
    
    try:
        clock = time.clock()
        frame_count = 0
        detection_count = 0
        
        print("进行30帧性能测试...")
        
        while frame_count < 30:
            clock.tick()
            
            img = sensor.snapshot()
            if not img:
                continue
            
            # 执行检测
            blobs = img.find_blobs([(0, 30)], pixels_threshold=300)
            
            # 过滤矩形
            rectangles = []
            for blob in blobs:
                rectangularity = blob.area() / (blob.w() * blob.h())
                if rectangularity >= 0.6:
                    rectangles.append(blob)
            
            detection_count += len(rectangles)
            
            # 绘制结果
            for rect in rectangles:
                img.draw_rectangle(rect.rect(), color=(0, 255, 0), thickness=2)
            
            fps = clock.fps()
            img.draw_string(5, 5, "FPS:{:.1f}".format(fps), color=(255, 255, 255), scale=2)
            img.draw_string(5, 25, "帧:{}".format(frame_count), color=(255, 255, 255), scale=1)
            img.draw_string(5, 40, "矩形:{}".format(len(rectangles)), color=(0, 255, 255), scale=2)
            
            lcd.display(img)
            
            frame_count += 1
            
            if frame_count % 10 == 0:
                print("已完成{}帧，当前FPS: {:.1f}".format(frame_count, fps))
        
        avg_fps = clock.fps()
        avg_detections = detection_count / frame_count
        
        print("✅ 性能测试完成")
        print("- 平均FPS: {:.1f}".format(avg_fps))
        print("- 平均每帧检测矩形数: {:.1f}".format(avg_detections))
        print("- 总检测矩形数: {}".format(detection_count))
        
        return True
        
    except Exception as e:
        print("❌ 性能测试失败:", str(e))
        return False

def run_all_tests():
    """运行所有测试"""
    print("K210黑色矩形识别 - 完整测试")
    print("=" * 40)
    
    tests = [
        ("硬件测试", test_hardware),
        ("图像捕获测试", test_image_capture),
        ("黑色检测测试", test_black_detection),
        ("矩形过滤测试", test_rectangle_filtering),
        ("性能测试", test_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print("\n" + "="*20)
        print("开始", test_name)
        print("="*20)
        
        try:
            if test_func():
                passed += 1
                print("✅ {} 通过".format(test_name))
            else:
                print("❌ {} 失败".format(test_name))
        except Exception as e:
            print("❌ {} 异常: {}".format(test_name, str(e)))
        
        time.sleep_ms(1000)  # 测试间隔
    
    print("\n" + "="*40)
    print("测试总结")
    print("="*40)
    print("通过: {}/{}".format(passed, total))
    print("成功率: {:.1f}%".format(passed/total*100))
    
    if passed == total:
        print("🎉 所有测试通过！黑色矩形识别功能正常")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

def cleanup():
    """清理资源"""
    try:
        sensor.shutdown()
        lcd.clear()
        print("✅ 资源清理完成")
    except:
        pass

def main():
    """主函数"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n推荐使用:")
            print("- 新手: exec(open('k210_simple_black_rect.py').read())")
            print("- 完整功能: exec(open('k210_black_rectangle.py').read())")
            print("- 高级功能: exec(open('k210_advanced_black_rect.py').read())")
            print("- 配置版本: exec(open('k210_black_rect_config.py').read())")
        
    except Exception as e:
        print("❌ 测试过程出错:", str(e))
    finally:
        cleanup()

if __name__ == "__main__":
    main()
