# K210人脸识别项目

## 项目简介
基于K210芯片的实时人脸识别系统，使用MicroPython开发，支持实时检测和显示人脸位置。

## 文件结构
```
k210/
├── k210_canmv_face.py        # CanMV官方兼容版(⭐推荐)
├── canmv_diagnostic.py       # CanMV专用诊断脚本
├── k210_face_detection.py    # 主程序文件(完整版)
├── k210_face_simple.py       # 简化版本(最少代码行数)
├── k210_face_micropython.py  # MicroPython兼容版
├── k210_face_robust.py       # 健壮兼容版
├── k210_diagnostic.py        # 通用环境诊断脚本
├── config.py                # 统一配置文件
├── test_face_detection.py    # 测试脚本
├── syntax_check.py          # 语法检查脚本
├── example_usage.py          # 使用示例
└── README.md                # 说明文档
```

## 硬件要求
- K210开发板
- 摄像头模块(OV2640推荐)
- LCD显示屏(320x240)
- SD卡(存储模型文件)

## 软件依赖
- MicroPython固件
- KPU库(K210专用AI加速库)
- 人脸检测模型文件(face_detection.kmodel)

## 安装步骤

### 1. 准备模型文件
将人脸检测模型文件`face_detection.kmodel`放置到SD卡根目录下。

### 2. 上传代码

**CanMV固件用户(推荐):**
- `k210_canmv_face.py` (CanMV官方兼容版)
- `canmv_diagnostic.py` (CanMV专用诊断脚本)

**其他固件用户:**
- `k210_face_robust.py` (健壮兼容版)
- `k210_diagnostic.py` (通用环境诊断脚本)

### 3. 环境检查

**CanMV固件:**
```python
exec(open('canmv_diagnostic.py').read())
```

**其他固件:**
```python
exec(open('k210_diagnostic.py').read())
```

### 4. 运行程序

**方式1: CanMV官方兼容版(⭐推荐)**
```python
exec(open('k210_canmv_face.py').read())
```

**方式2: 健壮兼容版**
```python
exec(open('k210_face_robust.py').read())
```

**方式3: MicroPython兼容版**
```python
exec(open('k210_face_micropython.py').read())
```

**方式4: 完整版**
```python
exec(open('k210_face_detection.py').read())
```

## 配置说明

### 主要配置参数(config.py)
- `MODEL_PATH`: 模型文件路径
- `DETECTION_THRESHOLD`: 检测阈值(0.0-1.0)
- `CAMERA_WIDTH/HEIGHT`: 摄像头分辨率
- `BOX_COLOR`: 人脸框颜色
- `FRAME_DELAY_MS`: 帧间延迟

### 性能调优
- 降低`DETECTION_THRESHOLD`可提高检测灵敏度
- 增加`FRAME_DELAY_MS`可降低CPU占用
- 调整摄像头分辨率平衡性能和精度

## 功能特性
- ✅ 实时人脸检测
- ✅ 多人脸同时识别
- ✅ 置信度显示
- ✅ 可配置检测参数
- ✅ 异常处理和资源清理

## 使用方法

### 基本使用
```python
from k210_face_detection import FaceDetector

# 创建检测器实例
detector = FaceDetector()

# 开始检测
detector.run()
```

### 自定义配置
修改`config.py`中的参数来调整检测行为：
```python
# 提高检测精度
Config.DETECTION_THRESHOLD = 0.8

# 修改显示颜色
Config.BOX_COLOR = (255, 0, 0)  # 红色框
```

## 故障排除

### 常见问题

1. **ImportError: no module named 'KPU'**

   **CanMV固件用户:**
   - 使用CanMV专用版本: `exec(open('k210_canmv_face.py').read())`
   - 运行CanMV诊断: `exec(open('canmv_diagnostic.py').read())`
   - 确认使用的是CanMV固件(不是MaixPy)

   **其他固件用户:**
   - 确认使用的是支持KPU的K210固件(MaixPy)
   - 尝试运行诊断脚本: `exec(open('k210_diagnostic.py').read())`
   - 使用健壮兼容版: `exec(open('k210_face_robust.py').read())`

2. **模型加载失败**
   - 检查SD卡中是否存在模型文件
   - 确认文件路径配置正确
   - 验证模型文件完整性

3. **摄像头初始化失败**
   - 检查摄像头连接
   - 确认摄像头型号兼容
   - 重启开发板后重试

4. **检测效果不佳**
   - 调整检测阈值
   - 改善光照条件
   - 确保人脸正面朝向摄像头

### 调试模式
程序包含详细的错误信息输出，可通过串口查看调试信息。

## 性能指标
- 检测速度: ~20FPS (320x240分辨率)
- 检测距离: 0.5-3米
- 支持人脸数量: 1-10个
- 内存占用: <2MB

## 更新日志
- v1.0: 初始版本，支持基本人脸检测功能
- 配置文件统一管理所有参数
- 优化代码结构和错误处理
- 新增简化版本(k210_face_simple.py)，最少代码行数实现核心功能
- 新增测试脚本和使用示例
- 支持中文注释和友好的中文提示
- 新增MicroPython兼容版本，解决语法兼容性问题

## 🚀 快速开始

### CanMV固件用户(推荐)
1. 将模型文件`face_detect_320x240.kmodel`放入SD卡
2. 上传`k210_canmv_face.py`和`canmv_diagnostic.py`到开发板
3. 运行环境诊断：
   ```python
   exec(open('canmv_diagnostic.py').read())
   ```
4. 运行CanMV官方兼容版：
   ```python
   exec(open('k210_canmv_face.py').read())
   ```

### 其他固件用户
1. 将模型文件`face_detection.kmodel`放入SD卡
2. 上传相应Python文件到K210开发板
3. 运行环境诊断：
   ```python
   exec(open('k210_diagnostic.py').read())
   ```
4. 运行健壮兼容版：
   ```python
   exec(open('k210_face_robust.py').read())
   ```

## 🔧 语法检查

在上传到K210之前，可以运行语法检查：
```python
python syntax_check.py
```

## 许可证
本项目采用MIT许可证。

## 技术支持
如遇问题请检查：
1. 硬件连接是否正确
2. 模型文件是否完整
3. 配置参数是否合理
