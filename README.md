# K210人脸识别项目

## 项目简介
基于K210芯片的实时人脸识别系统，使用MicroPython开发，支持实时检测和显示人脸位置。

## 文件结构
```
k210/
├── k210_canmv_face.py        # CanMV官方兼容版(⭐推荐)
├── k210_demo_no_model.py     # 无模型演示版(🎬无需SD卡)
├── k210_simple_camera.py     # 简化摄像头测试(🔧格式修复)
├── image_format_fix.py       # 图像格式诊断工具(🔍深度诊断)
├── k210_black_rectangle.py   # 黑色矩形识别(🔲完整版)
├── k210_simple_black_rect.py # 黑色矩形识别(⚡简化版)
├── k210_advanced_black_rect.py # 黑色矩形识别(🚀高级版)
├── k210_black_rect_config.py # 黑色矩形识别(⚙️配置版)
├── model_helper.py           # 模型文件助手(📁文件管理)
├── canmv_diagnostic.py       # CanMV专用诊断脚本
├── test_canmv.py            # CanMV快速测试
├── k210_face_detection.py    # 主程序文件(完整版)
├── k210_face_simple.py       # 简化版本(最少代码行数)
├── k210_face_micropython.py  # MicroPython兼容版
├── k210_face_robust.py       # 健壮兼容版
├── k210_diagnostic.py        # 通用环境诊断脚本
├── config.py                # 统一配置文件
├── test_face_detection.py    # 测试脚本
├── syntax_check.py          # 语法检查脚本
├── example_usage.py          # 使用示例
└── README.md                # 说明文档
```

## 硬件要求
- K210开发板
- 摄像头模块(OV2640推荐)
- LCD显示屏(320x240)
- SD卡(存储模型文件)

## 软件依赖
- MicroPython固件
- KPU库(K210专用AI加速库)
- 人脸检测模型文件(face_detection.kmodel)

## 安装步骤

### 1. 准备模型文件
将人脸检测模型文件`face_detection.kmodel`放置到SD卡根目录下。

### 2. 上传代码

**CanMV固件用户(推荐):**
- `k210_canmv_face.py` (CanMV官方兼容版)
- `canmv_diagnostic.py` (CanMV专用诊断脚本)

**其他固件用户:**
- `k210_face_robust.py` (健壮兼容版)
- `k210_diagnostic.py` (通用环境诊断脚本)

### 3. 环境检查

**CanMV固件:**
```python
exec(open('canmv_diagnostic.py').read())
```

**其他固件:**
```python
exec(open('k210_diagnostic.py').read())
```

### 4. 运行程序

**方式1: 无模型演示版(🎬立即可用)**
```python
exec(open('k210_demo_no_model.py').read())
```

**方式2: CanMV官方兼容版(⭐推荐)**
```python
exec(open('k210_canmv_face.py').read())
```

**方式3: 健壮兼容版**
```python
exec(open('k210_face_robust.py').read())
```

**方式4: MicroPython兼容版**
```python
exec(open('k210_face_micropython.py').read())
```

**方式5: 完整版**
```python
exec(open('k210_face_detection.py').read())
```

## 配置说明

### 主要配置参数(config.py)
- `MODEL_PATH`: 模型文件路径
- `DETECTION_THRESHOLD`: 检测阈值(0.0-1.0)
- `CAMERA_WIDTH/HEIGHT`: 摄像头分辨率
- `BOX_COLOR`: 人脸框颜色
- `FRAME_DELAY_MS`: 帧间延迟

### 性能调优
- 降低`DETECTION_THRESHOLD`可提高检测灵敏度
- 增加`FRAME_DELAY_MS`可降低CPU占用
- 调整摄像头分辨率平衡性能和精度

## 📁 存储选项

### 模型文件存储位置

**优先级顺序(自动搜索):**
1. `/flash/face_detect_320x240.kmodel` - 内部Flash存储(推荐)
2. `/sd/face_detect_320x240.kmodel` - SD卡存储
3. `face_detect_320x240.kmodel` - 当前目录
4. `/flash/face_detection.kmodel` - 备用模型名
5. `/sd/face_detection.kmodel` - 备用SD卡模型

**存储对比:**
| 存储类型 | 优点 | 缺点 | 推荐度 |
|---------|------|------|--------|
| 内部Flash | 无需SD卡，启动快 | 空间有限 | ⭐⭐⭐ |
| SD卡 | 空间大，易更换 | 需要SD卡 | ⭐⭐ |
| 无模型 | 立即可用 | 仅演示功能 | ⭐ |

### 💾 如何将模型放入内部Flash
1. 通过CanMV IDE上传文件到`/flash/`目录
2. 或使用文件管理工具复制到Flash分区
3. 确保文件名为`face_detect_320x240.kmodel`

## 功能特性
- ✅ 实时人脸检测
- ✅ 多人脸同时识别
- ✅ 置信度显示
- ✅ 可配置检测参数
- ✅ 异常处理和资源清理

## 使用方法

### 基本使用
```python
from k210_face_detection import FaceDetector

# 创建检测器实例
detector = FaceDetector()

# 开始检测
detector.run()
```

### 自定义配置
修改`config.py`中的参数来调整检测行为：
```python
# 提高检测精度
Config.DETECTION_THRESHOLD = 0.8

# 修改显示颜色
Config.BOX_COLOR = (255, 0, 0)  # 红色框
```

## 故障排除

### 常见问题

1. **ImportError: no module named 'KPU'**

   **CanMV固件用户:**
   - 使用CanMV专用版本: `exec(open('k210_canmv_face.py').read())`
   - 运行CanMV诊断: `exec(open('canmv_diagnostic.py').read())`
   - 确认使用的是CanMV固件(不是MaixPy)

   **其他固件用户:**
   - 确认使用的是支持KPU的K210固件(MaixPy)
   - 尝试运行诊断脚本: `exec(open('k210_diagnostic.py').read())`
   - 使用健壮兼容版: `exec(open('k210_face_robust.py').read())`

2. **模型加载失败 / 没有SD卡**

   **无SD卡用户:**
   - 🎬 直接运行演示版: `exec(open('k210_demo_no_model.py').read())`
   - 📁 检查文件状态: `exec(open('model_helper.py').read())`
   - 💾 将模型文件放入内部Flash: `/flash/face_detect_320x240.kmodel`

   **有SD卡用户:**
   - 检查SD卡中是否存在模型文件
   - 确认文件路径配置正确
   - 验证模型文件完整性

3. **摄像头初始化失败**
   - 检查摄像头连接
   - 确认摄像头型号兼容
   - 重启开发板后重试

4. **Image format is not supported (图像格式不支持)**

   **立即解决方案:**
   - 🔧 运行简化测试: `exec(open('k210_simple_camera.py').read())`
   - 🔍 完整诊断: `exec(open('image_format_fix.py').read())`

   **常见原因和解决方法:**
   - 摄像头连接不良 → 检查排线连接
   - 像素格式不兼容 → 自动切换到GRAYSCALE格式
   - 分辨率设置错误 → 降级使用QQVGA(160x120)
   - 初始化时间不足 → 增加等待时间到3秒
   - 固件版本问题 → 尝试不同的CanMV固件版本

5. **检测效果不佳**
   - 调整检测阈值
   - 改善光照条件
   - 确保人脸正面朝向摄像头

6. **黑色矩形检测问题**

   **检测不到矩形:**
   - 🔍 降低最小面积阈值: `MIN_AREA = 200`
   - 🔍 调整黑色阈值: `BLACK_THRESHOLD = [(0, 50)]`
   - 🔍 改善光照条件，增加对比度
   - 🔍 确保矩形足够黑且边界清晰

   **误检测过多:**
   - 🎯 提高最小面积阈值: `MIN_AREA = 1000`
   - 🎯 提高矩形度要求: `MIN_RECTANGULARITY = 0.8`
   - 🎯 限制长宽比: `MAX_ASPECT_RATIO = 3`
   - 🎯 使用LAB色彩空间检测

   **检测不稳定:**
   - ⚡ 使用简化版本: `k210_simple_black_rect.py`
   - ⚡ 增加帧间延迟: `FRAME_DELAY_MS = 100`
   - ⚡ 启用图像平滑处理
   - ⚡ 固定摄像头位置，避免抖动

### 调试模式
程序包含详细的错误信息输出，可通过串口查看调试信息。

## 性能指标
- 检测速度: ~20FPS (320x240分辨率)
- 检测距离: 0.5-3米
- 支持人脸数量: 1-10个
- 内存占用: <2MB

## 更新日志
- v1.0: 初始版本，支持基本人脸检测功能
- 配置文件统一管理所有参数
- 优化代码结构和错误处理
- 新增简化版本(k210_face_simple.py)，最少代码行数实现核心功能
- 新增测试脚本和使用示例
- 支持中文注释和友好的中文提示
- 新增MicroPython兼容版本，解决语法兼容性问题

## 🚀 快速开始

### 🎬 无SD卡/无模型文件用户
**立即体验，无需任何准备！**
```python
exec(open('k210_demo_no_model.py').read())
```
- ✅ 无需SD卡或模型文件
- ✅ 展示摄像头和图像处理功能
- ✅ 验证硬件工作状态
- ✅ 包含边缘检测和模拟人脸框演示

### 🔧 遇到"Image format is not supported"错误
**一键修复图像格式问题！**
```python
exec(open('k210_simple_camera.py').read())
```
- 🔧 自动检测兼容的图像格式
- 🔧 自动选择合适的分辨率
- 🔧 详细的错误诊断信息
- 🔧 生成修复后的代码

## 🔲 黑色矩形识别

### ⚡ 简化版本(推荐新手)
**最少代码，快速上手！**
```python
exec(open('k210_simple_black_rect.py').read())
```
- ⚡ 代码简洁，易于理解
- ⚡ 灰度模式检测，兼容性好
- ⚡ 实时显示检测结果
- ⚡ 自动过滤非矩形形状

### 🔲 完整版本(功能全面)
**完整功能，详细信息！**
```python
exec(open('k210_black_rectangle.py').read())
```
- 🔲 LAB色彩空间检测，精度更高
- 🔲 多种形状过滤算法
- 🔲 详细的矩形信息显示
- 🔲 完整的错误处理和诊断

### 🚀 高级版本(多模式)
**自动切换检测模式！**
```python
exec(open('k210_advanced_black_rect.py').read())
```
- 🚀 支持灰度/LAB/RGB三种检测模式
- 🚀 自动模式切换(每200帧)
- 🚀 高级形状分析和过滤
- 🚀 参数调整演示功能

### ⚙️ 配置版本(参数统一)
**使用统一配置文件！**
```python
exec(open('k210_black_rect_config.py').read())
```
- ⚙️ 从config.py统一管理参数
- ⚙️ 避免重复定义，便于维护
- ⚙️ 支持配置热更新
- ⚙️ 标准化的项目结构

### 📁 检查模型文件状态
```python
exec(open('model_helper.py').read())
```
- 🔍 自动搜索现有模型文件
- 📥 提供模型下载指导
- 💾 支持内部Flash和SD卡存储

### CanMV固件用户(推荐)
1. **可选**: 将模型文件`face_detect_320x240.kmodel`放入内部Flash(`/flash/`)或SD卡(`/sd/`)
2. 上传Python文件到开发板
3. 检查模型文件：
   ```python
   exec(open('model_helper.py').read())
   ```
4. 运行人脸识别：
   ```python
   exec(open('k210_canmv_face.py').read())
   ```

### 其他固件用户
1. 将模型文件`face_detection.kmodel`放入SD卡
2. 上传相应Python文件到K210开发板
3. 运行环境诊断：
   ```python
   exec(open('k210_diagnostic.py').read())
   ```
4. 运行健壮兼容版：
   ```python
   exec(open('k210_face_robust.py').read())
   ```

## 🔧 语法检查

在上传到K210之前，可以运行语法检查：
```python
python syntax_check.py
```

## 许可证
本项目采用MIT许可证。

## 技术支持
如遇问题请检查：
1. 硬件连接是否正确
2. 模型文件是否完整
3. 配置参数是否合理
