# K210人脸识别测试脚本
import sys
import time
import ast

class MockSensor:
    """模拟传感器类用于测试"""
    RGB565 = 1
    QVGA = 2
    
    @staticmethod
    def reset(): print("传感器重置")
    @staticmethod
    def set_pixformat(fmt): print(f"设置像素格式: {fmt}")
    @staticmethod
    def set_framesize(size): print(f"设置帧大小: {size}")
    @staticmethod
    def set_windowing(size): print(f"设置窗口: {size}")
    @staticmethod
    def set_vflip(flip): print(f"设置垂直翻转: {flip}")
    @staticmethod
    def run(mode): print(f"传感器运行模式: {mode}")
    @staticmethod
    def snapshot(): 
        print("获取图像快照")
        return MockImage()
    @staticmethod
    def shutdown(): print("传感器关闭")

class MockLCD:
    """模拟LCD类用于测试"""
    BLACK = 0
    
    @staticmethod
    def init(): print("LCD初始化")
    @staticmethod
    def clear(color=None): print(f"LCD清屏: {color}")
    @staticmethod
    def display(img): print("LCD显示图像")

class MockKPU:
    """模拟KPU类用于测试"""
    @staticmethod
    def load(path): 
        print(f"加载模型: {path}")
        return "mock_task"
    
    @staticmethod
    def init_yolo2(task, threshold, nms, anchor_num, anchors):
        print(f"初始化YOLO2: 阈值={threshold}, NMS={nms}, 锚点数={anchor_num}")
    
    @staticmethod
    def run_yolo2(task, img):
        print("运行YOLO2检测")
        return [MockDetection()] if time.time() % 2 < 1 else []  # 模拟间歇性检测
    
    @staticmethod
    def deinit(task): print("KPU资源清理")

class MockDetection:
    """模拟检测结果类"""
    def x(self): return 50
    def y(self): return 50  
    def w(self): return 100
    def h(self): return 120
    def value(self): return 0.85

class MockImage:
    """模拟图像类"""
    def draw_rectangle(self, x, y, w, h, color, thickness):
        print(f"绘制矩形: ({x},{y}) 大小({w}x{h}) 颜色{color} 粗细{thickness}")
    
    def draw_string(self, x, y, text, color, scale):
        print(f"绘制文字: ({x},{y}) 内容'{text}' 颜色{color} 大小{scale}")

def mock_k210_modules():
    """模拟K210模块"""
    sys.modules['sensor'] = MockSensor()
    sys.modules['lcd'] = MockLCD()
    sys.modules['KPU'] = MockKPU()
    sys.modules['image'] = type('MockModule', (), {})()
    sys.modules['machine'] = type('MockModule', (), {'UART': None})()
    sys.modules['fpioa_manager'] = type('MockModule', (), {'fm': None})()

def check_syntax(filename):
    """检查Python文件语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        ast.parse(source)
        print(f"✓ {filename} 语法检查通过")
        return True
    except SyntaxError as e:
        print(f"✗ {filename} 语法错误: {e}")
        print(f"  行号: {e.lineno}, 位置: {e.offset}")
        return False
    except Exception as e:
        print(f"✗ {filename} 检查失败: {e}")
        return False

def test_face_detection():
    """测试人脸检测功能"""
    print("=== K210人脸识别测试开始 ===")

    # 首先检查所有文件的语法
    files_to_check = [
        'config.py',
        'k210_face_detection.py',
        'k210_face_simple.py',
        'example_usage.py'
    ]

    syntax_ok = True
    for filename in files_to_check:
        if not check_syntax(filename):
            syntax_ok = False

    if not syntax_ok:
        print("\n✗ 存在语法错误，请修复后重试")
        return

    print("\n=== 语法检查全部通过 ===")

    # 模拟K210环境
    mock_k210_modules()

    try:
        # 测试配置文件
        from config import Config
        print(f"✓ 配置加载成功")
        print(f"  模型路径: {Config.MODEL_PATH}")
        print(f"  检测阈值: {Config.DETECTION_THRESHOLD}")

        print("\n✓ 所有测试通过")

    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_face_detection()
