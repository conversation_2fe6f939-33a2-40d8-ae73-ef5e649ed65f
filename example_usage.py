# K210人脸识别使用示例

"""
使用方法1: 完整版本 (推荐用于生产环境)
"""
def run_full_version():
    from k210_face_detection import FaceDetector
    
    # 创建检测器实例
    detector = FaceDetector()
    
    # 开始检测 (会一直运行直到手动停止)
    detector.run()

"""
使用方法2: 简化版本 (适合快速测试)
"""
def run_simple_version():
    # 直接执行简化版代码
    exec(open('k210_face_simple.py').read())

"""
使用方法3: 自定义配置
"""
def run_with_custom_config():
    from config import Config
    from k210_face_detection import FaceDetector
    
    # 修改配置参数
    Config.DETECTION_THRESHOLD = 0.8  # 提高检测精度
    Config.BOX_COLOR = (255, 0, 0)    # 使用红色框
    Config.FRAME_DELAY_MS = 100       # 降低帧率节省资源
    
    # 创建并运行检测器
    detector = FaceDetector()
    detector.run()

# 主程序入口
if __name__ == "__main__":
    print("K210人脸识别使用示例")
    print("1. 完整版本")
    print("2. 简化版本") 
    print("3. 自定义配置")
    
    choice = input("请选择运行模式 (1-3): ")
    
    if choice == "1":
        run_full_version()
    elif choice == "2":
        run_simple_version()
    elif choice == "3":
        run_with_custom_config()
    else:
        print("无效选择，使用默认完整版本")
        run_full_version()
