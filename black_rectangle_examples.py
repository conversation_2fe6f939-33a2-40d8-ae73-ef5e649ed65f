# K210黑色矩形识别使用示例
# 展示各种使用方法和参数调优技巧

def show_basic_usage():
    """基本使用方法"""
    print("=== 基本使用方法 ===")
    
    print("1. 简化版本(新手推荐):")
    print("   exec(open('k210_simple_black_rect.py').read())")
    print("   - 代码最少，易于理解")
    print("   - 灰度模式，兼容性好")
    print("   - 基本的矩形检测功能")
    
    print("\n2. 完整版本(功能全面):")
    print("   exec(open('k210_black_rectangle.py').read())")
    print("   - LAB色彩空间，精度更高")
    print("   - 完整的错误处理")
    print("   - 详细的检测信息")
    
    print("\n3. 高级版本(多模式):")
    print("   exec(open('k210_advanced_black_rect.py').read())")
    print("   - 支持多种检测模式")
    print("   - 自动模式切换")
    print("   - 参数调整演示")
    
    print("\n4. 配置版本(参数统一):")
    print("   exec(open('k210_black_rect_config.py').read())")
    print("   - 使用统一配置文件")
    print("   - 便于参数管理")
    print("   - 标准化项目结构")

def show_parameter_tuning():
    """参数调优示例"""
    print("\n=== 参数调优示例 ===")
    
    print("1. 黑色检测阈值调优:")
    print("   # 环境较暗，需要更宽松的阈值")
    print("   BLACK_THRESHOLD = [(0, 40)]")
    print("   ")
    print("   # 环境较亮，需要更严格的阈值")
    print("   BLACK_THRESHOLD = [(0, 15)]")
    print("   ")
    print("   # 标准环境")
    print("   BLACK_THRESHOLD = [(0, 30)]")
    
    print("\n2. 面积过滤调优:")
    print("   # 检测小矩形(如二维码)")
    print("   MIN_AREA = 100")
    print("   MAX_AREA = 5000")
    print("   ")
    print("   # 检测中等矩形(如标签)")
    print("   MIN_AREA = 500")
    print("   MAX_AREA = 20000")
    print("   ")
    print("   # 检测大矩形(如屏幕)")
    print("   MIN_AREA = 2000")
    print("   MAX_AREA = 50000")
    
    print("\n3. 形状过滤调优:")
    print("   # 宽松的矩形要求(允许略微变形)")
    print("   MIN_RECTANGULARITY = 0.5")
    print("   MAX_ASPECT_RATIO = 8")
    print("   ")
    print("   # 标准矩形要求")
    print("   MIN_RECTANGULARITY = 0.6")
    print("   MAX_ASPECT_RATIO = 5")
    print("   ")
    print("   # 严格的矩形要求(接近完美矩形)")
    print("   MIN_RECTANGULARITY = 0.8")
    print("   MAX_ASPECT_RATIO = 3")

def show_troubleshooting():
    """故障排除示例"""
    print("\n=== 故障排除示例 ===")
    
    print("1. 检测不到矩形:")
    print("   问题: 阈值过严或面积过大")
    print("   解决: 降低阈值，减小最小面积")
    print("   BLACK_THRESHOLD = [(0, 50)]")
    print("   MIN_AREA = 200")
    print("   MIN_RECTANGULARITY = 0.4")
    
    print("\n2. 误检测过多:")
    print("   问题: 阈值过宽或过滤不严")
    print("   解决: 提高阈值，增强过滤")
    print("   BLACK_THRESHOLD = [(0, 20)]")
    print("   MIN_AREA = 1000")
    print("   MIN_RECTANGULARITY = 0.8")
    print("   MAX_ASPECT_RATIO = 3")
    
    print("\n3. 检测不稳定:")
    print("   问题: 光照变化或摄像头抖动")
    print("   解决: 增加延迟，固定摄像头")
    print("   FRAME_DELAY_MS = 100")
    print("   # 使用三脚架固定摄像头")
    print("   # 保持稳定的光照条件")

def show_custom_detection():
    """自定义检测示例"""
    print("\n=== 自定义检测示例 ===")
    
    print("1. 自定义检测函数:")
    print("""
def custom_black_detect(img):
    # 自定义黑色阈值
    threshold = [(0, 25)]
    
    # 查找黑色区域
    blobs = img.find_blobs(threshold, pixels_threshold=400, merge=True)
    
    rectangles = []
    for blob in blobs:
        # 自定义过滤条件
        area = blob.area()
        w, h = blob.w(), blob.h()
        
        # 计算矩形度
        rect_ratio = area / (w * h)
        
        # 计算长宽比
        aspect_ratio = max(w, h) / min(w, h)
        
        # 自定义过滤规则
        if (area > 300 and area < 10000 and 
            rect_ratio > 0.6 and 
            aspect_ratio < 4):
            rectangles.append(blob)
    
    return rectangles
""")
    
    print("2. 多条件检测:")
    print("""
def multi_condition_detect(img):
    # 检测不同大小的矩形
    small_rects = find_rectangles(img, min_area=100, max_area=1000)
    medium_rects = find_rectangles(img, min_area=1000, max_area=5000)
    large_rects = find_rectangles(img, min_area=5000, max_area=20000)
    
    return {
        'small': small_rects,
        'medium': medium_rects,
        'large': large_rects
    }
""")

def show_performance_optimization():
    """性能优化示例"""
    print("\n=== 性能优化示例 ===")
    
    print("1. 降低分辨率提高速度:")
    print("   sensor.set_framesize(sensor.QQVGA)  # 160x120")
    print("   # 检测速度提高约2倍")
    
    print("\n2. 调整帧率:")
    print("   FRAME_DELAY_MS = 100  # 降低到10FPS")
    print("   # 减少CPU占用，提高稳定性")
    
    print("\n3. 垃圾回收优化:")
    print("   GC_INTERVAL = 5  # 每5帧回收一次")
    print("   # 防止内存泄漏，保持稳定运行")
    
    print("\n4. 简化绘制:")
    print("   # 只绘制检测框，不绘制详细信息")
    print("   img.draw_rectangle(rect, color=(0,255,0), thickness=2)")
    print("   # 减少绘制时间")

def show_integration_examples():
    """集成应用示例"""
    print("\n=== 集成应用示例 ===")
    
    print("1. 与人脸检测结合:")
    print("""
def face_and_rectangle_detect():
    # 同时检测人脸和黑色矩形
    face_rects = detect_faces(img)
    black_rects = detect_black_rectangles(img)
    
    # 分别用不同颜色标记
    for face in face_rects:
        img.draw_rectangle(face, color=(0,255,0))  # 绿色
    
    for rect in black_rects:
        img.draw_rectangle(rect, color=(255,0,0))  # 红色
""")
    
    print("2. 区域计数应用:")
    print("""
def count_rectangles_in_regions():
    rectangles = detect_black_rectangles(img)
    
    # 定义感兴趣区域
    roi_left = (0, 0, 160, 240)
    roi_right = (160, 0, 160, 240)
    
    left_count = 0
    right_count = 0
    
    for rect in rectangles:
        cx = rect['cx']
        if cx < 160:
            left_count += 1
        else:
            right_count += 1
    
    print("左侧: {} 个, 右侧: {} 个".format(left_count, right_count))
""")

def show_testing_examples():
    """测试示例"""
    print("\n=== 测试示例 ===")
    
    print("1. 运行完整测试:")
    print("   exec(open('test_black_rectangle.py').read())")
    print("   # 测试所有功能模块")
    
    print("\n2. 单独测试硬件:")
    print("""
def test_hardware_only():
    import sensor, lcd
    
    lcd.init()
    sensor.reset()
    sensor.set_pixformat(sensor.GRAYSCALE)
    sensor.set_framesize(sensor.QVGA)
    sensor.run(1)
    
    print("硬件测试通过")
""")
    
    print("3. 测试检测精度:")
    print("""
def test_detection_accuracy():
    correct_detections = 0
    total_frames = 100
    
    for i in range(total_frames):
        img = sensor.snapshot()
        rectangles = detect_black_rectangles(img)
        
        # 人工验证检测结果
        if verify_detection(rectangles):
            correct_detections += 1
    
    accuracy = correct_detections / total_frames
    print("检测精度: {:.1f}%".format(accuracy * 100))
""")

def main():
    """主函数"""
    print("K210黑色矩形识别 - 使用示例大全")
    print("=" * 45)
    
    show_basic_usage()
    show_parameter_tuning()
    show_troubleshooting()
    show_custom_detection()
    show_performance_optimization()
    show_integration_examples()
    show_testing_examples()
    
    print("\n" + "=" * 45)
    print("建议使用流程:")
    print("1. 先运行测试: exec(open('test_black_rectangle.py').read())")
    print("2. 选择合适版本开始使用")
    print("3. 根据实际效果调整参数")
    print("4. 集成到自己的应用中")

if __name__ == "__main__":
    main()
