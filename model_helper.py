# K210模型文件助手
# 检查模型文件状态并提供下载指导

import os

def check_storage():
    """检查存储设备状态"""
    print("=== 存储设备检查 ===")
    
    # 检查内部Flash
    try:
        flash_files = os.listdir('/flash')
        print("✅ 内部Flash可访问")
        print("Flash文件数量:", len(flash_files))
        
        # 检查可用空间(如果支持)
        try:
            stat = os.statvfs('/flash')
            free_space = stat[0] * stat[3]  # 块大小 * 可用块数
            print("Flash可用空间: 约{}KB".format(free_space // 1024))
        except:
            print("Flash空间信息不可用")
            
    except Exception as e:
        print("❌ 内部Flash访问失败:", str(e))
    
    # 检查SD卡
    try:
        sd_files = os.listdir('/sd')
        print("✅ SD卡可访问")
        print("SD卡文件数量:", len(sd_files))
        
        try:
            stat = os.statvfs('/sd')
            free_space = stat[0] * stat[3]
            print("SD卡可用空间: 约{}KB".format(free_space // 1024))
        except:
            print("SD卡空间信息不可用")
            
    except Exception as e:
        print("❌ SD卡访问失败:", str(e))
        print("提示: 请插入SD卡或检查SD卡格式")

def find_model_files():
    """查找现有的模型文件"""
    print("\n=== 模型文件搜索 ===")
    
    search_paths = [
        ('/flash', '内部Flash'),
        ('/sd', 'SD卡'),
        ('.', '当前目录')
    ]
    
    model_extensions = ['.kmodel', '.model']
    found_models = []
    
    for path, name in search_paths:
        try:
            files = os.listdir(path)
            print("\n📁 检查{}:".format(name))
            
            path_models = []
            for file in files:
                for ext in model_extensions:
                    if file.endswith(ext):
                        full_path = path + '/' + file if path != '.' else file
                        path_models.append((full_path, file))
                        found_models.append((full_path, file, name))
            
            if path_models:
                for full_path, filename in path_models:
                    try:
                        stat = os.stat(full_path)
                        size = stat[6]
                        print("  ✅ {} ({}KB)".format(filename, size // 1024))
                    except:
                        print("  ✅ {}".format(filename))
            else:
                print("  ❌ 未找到模型文件")
                
        except Exception as e:
            print("  ❌ 无法访问{}: {}".format(name, str(e)))
    
    return found_models

def show_model_info():
    """显示模型文件信息"""
    print("\n=== 推荐模型文件 ===")
    
    models = [
        {
            'name': 'face_detect_320x240.kmodel',
            'desc': 'CanMV官方人脸检测模型',
            'size': '约300-500KB',
            'input': '320x240 RGB565',
            'output': 'YOLO2格式人脸框'
        },
        {
            'name': 'face_detection.kmodel', 
            'desc': 'MaixPy通用人脸检测模型',
            'size': '约200-400KB',
            'input': '320x240 RGB565',
            'output': 'YOLO2格式人脸框'
        }
    ]
    
    for i, model in enumerate(models, 1):
        print("\n{}. {}".format(i, model['name']))
        print("   描述: {}".format(model['desc']))
        print("   大小: {}".format(model['size']))
        print("   输入: {}".format(model['input']))
        print("   输出: {}".format(model['output']))

def show_download_guide():
    """显示下载指导"""
    print("\n=== 模型文件获取指导 ===")
    
    print("\n📥 方法1: 官方模型库")
    print("1. 访问CanMV官方GitHub:")
    print("   https://github.com/kendryte/canmv_examples")
    print("2. 下载对应的.kmodel文件")
    print("3. 将文件复制到SD卡或内部Flash")
    
    print("\n📥 方法2: MaixHub模型库")
    print("1. 访问MaixHub: https://maixhub.com/")
    print("2. 搜索'人脸检测'或'face detection'")
    print("3. 下载K210兼容的模型文件")
    
    print("\n📥 方法3: 自训练模型")
    print("1. 使用nncase工具转换模型")
    print("2. 确保输入尺寸为320x240")
    print("3. 输出格式为YOLO2")

def show_usage_guide():
    """显示使用指导"""
    print("\n=== 使用指导 ===")
    
    print("\n🎯 有模型文件时:")
    print("exec(open('k210_canmv_face.py').read())")
    
    print("\n🎬 无模型文件时(演示模式):")
    print("exec(open('k210_demo_no_model.py').read())")
    
    print("\n🔧 环境检查:")
    print("exec(open('canmv_diagnostic.py').read())")
    
    print("\n💡 推荐存储位置:")
    print("1. 内部Flash: /flash/face_detect_320x240.kmodel")
    print("2. SD卡: /sd/face_detect_320x240.kmodel")
    print("3. 当前目录: face_detect_320x240.kmodel")

def create_test_model_info():
    """创建测试用的模型信息文件"""
    print("\n=== 创建模型信息文件 ===")
    
    try:
        info_content = """# K210人脸检测模型信息
# 此文件用于记录模型文件状态

模型文件要求:
- 文件名: face_detect_320x240.kmodel 或 face_detection.kmodel
- 大小: 200KB - 500KB
- 格式: K210 kmodel格式
- 输入: 320x240 RGB565图像
- 输出: YOLO2格式检测框

下载地址:
1. CanMV官方: https://github.com/kendryte/canmv_examples
2. MaixHub: https://maixhub.com/
3. Sipeed: https://dl.sipeed.com/

使用方法:
1. 将模型文件放入 /flash/ 或 /sd/ 目录
2. 运行: exec(open('k210_canmv_face.py').read())
"""
        
        with open('model_info.txt', 'w') as f:
            f.write(info_content)
        
        print("✅ 已创建模型信息文件: model_info.txt")
        
    except Exception as e:
        print("❌ 创建信息文件失败:", str(e))

def main():
    """主函数"""
    print("K210模型文件助手")
    print("=" * 30)
    
    # 检查存储设备
    check_storage()
    
    # 查找现有模型
    found_models = find_model_files()
    
    if found_models:
        print("\n🎉 找到{}个模型文件:".format(len(found_models)))
        for full_path, filename, location in found_models:
            print("  📄 {} (位于{})".format(filename, location))
        print("\n✅ 可以直接运行人脸识别程序!")
        print("exec(open('k210_canmv_face.py').read())")
    else:
        print("\n❌ 未找到模型文件")
        
        # 显示模型信息
        show_model_info()
        
        # 显示下载指导
        show_download_guide()
        
        # 创建信息文件
        create_test_model_info()
        
        print("\n🎬 可以先运行演示模式:")
        print("exec(open('k210_demo_no_model.py').read())")
    
    # 显示使用指导
    show_usage_guide()

if __name__ == "__main__":
    main()
