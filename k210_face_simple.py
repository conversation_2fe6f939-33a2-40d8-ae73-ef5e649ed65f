# K210人脸识别简化版
import sensor, image, time, lcd, KPU as kpu
from config import Config

# 初始化硬件
lcd.init(); lcd.clear(lcd.BLACK)  # 初始化LCD
sensor.reset(); sensor.set_pixformat(sensor.RGB565); sensor.set_framesize(sensor.QVGA)  # 初始化摄像头
sensor.set_windowing((Config.CAMERA_WIDTH, Config.CAMERA_HEIGHT)); sensor.set_vflip(Config.CAMERA_VFLIP); sensor.run(1)

# 加载模型
task = kpu.load(Config.MODEL_PATH)
kpu.init_yolo2(task, Config.DETECTION_THRESHOLD, Config.NMS_THRESHOLD, Config.ANCHOR_NUM, Config.ANCHOR_POINTS)

print("K210人脸识别启动完成")

try:
    while True:
        img = sensor.snapshot()  # 获取图像
        code = kpu.run_yolo2(task, img)  # 运行检测
        if code:  # 如果检测到人脸
            for i in code:
                if i.value() > Config.DETECTION_THRESHOLD:  # 置信度过滤
                    img.draw_rectangle(i.x(), i.y(), i.w(), i.h(), color=Config.BOX_COLOR, thickness=Config.BOX_THICKNESS)  # 绘制框
                    img.draw_string(i.x(), i.y()-20, "{:.2f}".format(i.value()), color=Config.TEXT_COLOR, scale=Config.TEXT_SCALE)  # 显示置信度
        lcd.display(img)  # 显示图像
        time.sleep_ms(Config.FRAME_DELAY_MS)  # 帧率控制
except KeyboardInterrupt:
    print("程序结束")
finally:
    kpu.deinit(task); sensor.shutdown(); lcd.clear()  # 清理资源
