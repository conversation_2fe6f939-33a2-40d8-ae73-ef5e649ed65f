# 简单的语法检查脚本
import ast
import os

def check_syntax(filename):
    """检查Python文件语法"""
    if not os.path.exists(filename):
        print("文件不存在: {}".format(filename))
        return False
        
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        ast.parse(source)
        print("✓ {} 语法检查通过".format(filename))
        return True
    except SyntaxError as e:
        print("✗ {} 语法错误:".format(filename))
        print("  错误信息: {}".format(e.msg))
        print("  行号: {}".format(e.lineno))
        if e.offset:
            print("  位置: {}".format(e.offset))
        return False
    except Exception as e:
        print("✗ {} 检查失败: {}".format(filename, e))
        return False

def main():
    print("=== K210人脸识别项目语法检查 ===")
    
    files_to_check = [
        'config.py',
        'k210_face_detection.py', 
        'k210_face_simple.py',
        'example_usage.py'
    ]
    
    all_ok = True
    for filename in files_to_check:
        if not check_syntax(filename):
            all_ok = False
    
    if all_ok:
        print("\n✓ 所有文件语法检查通过！")
    else:
        print("\n✗ 存在语法错误，请修复后重试")

if __name__ == "__main__":
    main()
