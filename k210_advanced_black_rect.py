# K210高级黑色矩形识别 - 多种检测模式
# 支持不同的检测算法和参数调整

import sensor, image, time, lcd
import gc

# 检测模式配置
DETECTION_MODES = {
    'grayscale': {
        'name': '灰度检测',
        'threshold': [(0, 30)],  # 灰度阈值
        'pixformat': sensor.GRAYSCALE
    },
    'lab': {
        'name': 'LAB色彩检测', 
        'threshold': [(0, 30, -128, 127, -128, 127)],  # LAB阈值
        'pixformat': sensor.RGB565
    },
    'rgb': {
        'name': 'RGB检测',
        'threshold': [(0, 30, 0, 30, 0, 30)],  # RGB阈值
        'pixformat': sensor.RGB565
    }
}

# 当前检测模式
current_mode = 'grayscale'
MIN_AREA = 500
MAX_AREA = 20000
MIN_RECTANGULARITY = 0.6  # 最小矩形度

def init_hardware(mode='grayscale'):
    """初始化硬件"""
    print("初始化硬件 - {}模式".format(DETECTION_MODES[mode]['name']))
    
    lcd.init()
    lcd.clear(lcd.BLACK)
    
    sensor.reset()
    sensor.set_pixformat(DETECTION_MODES[mode]['pixformat'])
    
    try:
        sensor.set_framesize(sensor.QVGA)
    except:
        sensor.set_framesize(sensor.QQVGA)
    
    sensor.set_vflip(1)
    sensor.skip_frames(time=2000)
    sensor.run(1)
    
    print("✅ 硬件初始化完成")

def detect_rectangles_grayscale(img):
    """灰度模式检测"""
    threshold = DETECTION_MODES['grayscale']['threshold']
    blobs = img.find_blobs(threshold, pixels_threshold=MIN_AREA, merge=True)
    return filter_rectangles(blobs)

def detect_rectangles_lab(img):
    """LAB色彩模式检测(兼容性修复)"""
    try:
        # 尝试使用to_lab方法
        if hasattr(img, 'to_lab'):
            img_lab = img.to_lab()
            threshold = DETECTION_MODES['lab']['threshold']
            blobs = img_lab.find_blobs(threshold, pixels_threshold=MIN_AREA, merge=True)
        else:
            # 如果不支持to_lab，使用灰度检测
            print("⚠️ 不支持to_lab，使用灰度检测")
            threshold = DETECTION_MODES['grayscale']['threshold']
            blobs = img.find_blobs(threshold, pixels_threshold=MIN_AREA, merge=True)
        return filter_rectangles(blobs)
    except Exception as e:
        print("❌ LAB检测失败，回退到灰度检测:", str(e))
        threshold = DETECTION_MODES['grayscale']['threshold']
        blobs = img.find_blobs(threshold, pixels_threshold=MIN_AREA, merge=True)
        return filter_rectangles(blobs)

def detect_rectangles_rgb(img):
    """RGB模式检测"""
    threshold = DETECTION_MODES['rgb']['threshold']
    blobs = img.find_blobs(threshold, pixels_threshold=MIN_AREA, merge=True)
    return filter_rectangles(blobs)

def filter_rectangles(blobs):
    """过滤矩形"""
    rectangles = []
    
    for blob in blobs:
        # 面积过滤
        if not (MIN_AREA <= blob.area() <= MAX_AREA):
            continue
        
        # 矩形度过滤
        rectangularity = blob.area() / (blob.w() * blob.h())
        if rectangularity < MIN_RECTANGULARITY:
            continue
        
        # 长宽比过滤(避免过于细长的形状)
        aspect_ratio = max(blob.w(), blob.h()) / min(blob.w(), blob.h())
        if aspect_ratio > 5:  # 长宽比不超过5:1
            continue
        
        rectangles.append({
            'blob': blob,
            'rectangularity': rectangularity,
            'aspect_ratio': aspect_ratio,
            'area': blob.area()
        })
    
    # 按面积排序
    rectangles.sort(key=lambda r: r['area'], reverse=True)
    return rectangles

def draw_rectangle_advanced(img, rect_info, index):
    """高级矩形绘制"""
    blob = rect_info['blob']
    
    # 基本矩形框
    img.draw_rectangle(blob.rect(), color=(0, 255, 0), thickness=2)
    
    # 中心点和十字线
    cx, cy = blob.cx(), blob.cy()
    img.draw_cross(cx, cy, color=(255, 0, 0), size=10, thickness=2)
    
    # 对角线(显示旋转)
    x, y, w, h = blob.x(), blob.y(), blob.w(), blob.h()
    img.draw_line(x, y, x+w, y+h, color=(255, 255, 0), thickness=1)
    img.draw_line(x+w, y, x, y+h, color=(255, 255, 0), thickness=1)
    
    # 信息显示
    info_x = x + w + 5
    info_y = y
    
    # 基本信息
    img.draw_string(info_x, info_y, "矩形{}".format(index+1), color=(255, 255, 255), scale=1)
    img.draw_string(info_x, info_y+12, "{}x{}".format(w, h), color=(0, 255, 255), scale=1)
    img.draw_string(info_x, info_y+24, "面积:{}".format(rect_info['area']), color=(255, 255, 0), scale=1)
    img.draw_string(info_x, info_y+36, "矩形度:{:.2f}".format(rect_info['rectangularity']), color=(255, 0, 255), scale=1)
    img.draw_string(info_x, info_y+48, "长宽比:{:.1f}".format(rect_info['aspect_ratio']), color=(0, 255, 0), scale=1)
    
    # 在矩形内显示编号
    img.draw_string(cx-10, cy-5, str(index+1), color=(255, 255, 255), scale=3)

def run_detection():
    """运行检测"""
    global current_mode
    
    clock = time.clock()
    frame_count = 0
    mode_switch_frame = 0
    
    # 检测函数映射
    detect_functions = {
        'grayscale': detect_rectangles_grayscale,
        'lab': detect_rectangles_lab,
        'rgb': detect_rectangles_rgb
    }
    
    print("开始高级黑色矩形检测...")
    print("模式会每200帧自动切换")
    print("当前模式:", DETECTION_MODES[current_mode]['name'])
    
    try:
        while True:
            clock.tick()
            
            # 模式切换(每200帧)
            if frame_count - mode_switch_frame >= 200:
                modes = list(DETECTION_MODES.keys())
                current_index = modes.index(current_mode)
                current_mode = modes[(current_index + 1) % len(modes)]
                
                print("切换到模式:", DETECTION_MODES[current_mode]['name'])
                init_hardware(current_mode)
                mode_switch_frame = frame_count
                continue
            
            # 获取图像
            img = sensor.snapshot()
            if not img:
                continue
            
            # 执行检测
            detect_func = detect_functions[current_mode]
            rectangles = detect_func(img)
            
            # 绘制结果
            for i, rect_info in enumerate(rectangles):
                draw_rectangle_advanced(img, rect_info, i)
            
            # 显示状态信息
            fps = clock.fps()
            img.draw_string(5, 5, "模式:{}".format(DETECTION_MODES[current_mode]['name']), color=(255, 255, 255), scale=1)
            img.draw_string(5, 20, "FPS:{:.1f}".format(fps), color=(255, 255, 255), scale=2)
            img.draw_string(5, 40, "矩形:{}".format(len(rectangles)), color=(0, 255, 255), scale=2)
            img.draw_string(5, 60, "帧:{}".format(frame_count), color=(255, 255, 0), scale=1)
            
            # 显示参数
            img.draw_string(5, 220, "最小面积:{}".format(MIN_AREA), color=(128, 128, 128), scale=1)
            
            # 打印检测结果
            if rectangles:
                print("第{}帧[{}]: 检测到{}个矩形".format(
                    frame_count, DETECTION_MODES[current_mode]['name'], len(rectangles)))
                
                for i, rect_info in enumerate(rectangles):
                    blob = rect_info['blob']
                    print("  矩形{}: ({},{}) {}x{} 面积:{} 矩形度:{:.2f}".format(
                        i+1, blob.x(), blob.y(), blob.w(), blob.h(), 
                        rect_info['area'], rect_info['rectangularity']))
            
            lcd.display(img)
            frame_count += 1
            
            # 垃圾回收
            if frame_count % 20 == 0:
                gc.collect()
            
            time.sleep_ms(50)
            
    except KeyboardInterrupt:
        print("程序被用户中断")
        print("总帧数:", frame_count)
    except Exception as e:
        print("❌ 检测过程出错:", str(e))

def adjust_parameters():
    """参数调整演示"""
    global MIN_AREA, MAX_AREA, MIN_RECTANGULARITY
    
    print("\n=== 参数调整演示 ===")
    
    # 测试不同参数
    test_params = [
        {'min_area': 200, 'max_area': 10000, 'min_rect': 0.5, 'name': '宽松检测'},
        {'min_area': 500, 'max_area': 20000, 'min_rect': 0.7, 'name': '标准检测'},
        {'min_area': 1000, 'max_area': 50000, 'min_rect': 0.8, 'name': '严格检测'}
    ]
    
    for params in test_params:
        print("测试参数组合: {}".format(params['name']))
        MIN_AREA = params['min_area']
        MAX_AREA = params['max_area'] 
        MIN_RECTANGULARITY = params['min_rect']
        
        print("- 最小面积: {}".format(MIN_AREA))
        print("- 最大面积: {}".format(MAX_AREA))
        print("- 最小矩形度: {}".format(MIN_RECTANGULARITY))
        
        # 简单测试
        for i in range(10):
            img = sensor.snapshot()
            if img:
                rectangles = detect_rectangles_grayscale(img)
                print("  第{}帧: 检测到{}个矩形".format(i+1, len(rectangles)))
                time.sleep_ms(200)
        
        print("参数测试完成\n")

def show_info():
    """显示程序信息"""
    print("K210高级黑色矩形识别程序")
    print("=" * 35)
    print("支持的检测模式:")
    for key, mode in DETECTION_MODES.items():
        print("- {}: {}".format(key, mode['name']))
    
    print("\n检测参数:")
    print("- 最小面积:", MIN_AREA)
    print("- 最大面积:", MAX_AREA)
    print("- 最小矩形度:", MIN_RECTANGULARITY)
    
    print("\n功能特性:")
    print("- 自动模式切换")
    print("- 多种颜色空间检测")
    print("- 高级形状过滤")
    print("- 详细信息显示")

def main():
    """主函数"""
    show_info()
    
    try:
        init_hardware(current_mode)
        
        # 参数调整演示(可选)
        # adjust_parameters()
        
        # 运行检测
        run_detection()
        
    except Exception as e:
        print("❌ 程序运行出错:", str(e))
    finally:
        try:
            sensor.shutdown()
            lcd.clear()
        except:
            pass

if __name__ == "__main__":
    main()
