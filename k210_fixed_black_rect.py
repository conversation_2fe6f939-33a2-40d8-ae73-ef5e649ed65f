# K210黑色矩形识别 - 错误修复版
# 自动生成的修复代码，解决常见兼容性问题

import sensor, image, time, lcd
import gc

def safe_init():
    """安全的硬件初始化"""
    print("初始化硬件(安全模式)...")
    
    try:
        lcd.init()
        lcd.clear(lcd.BLACK)
        print("✅ LCD初始化成功")
    except Exception as e:
        print("❌ LCD初始化失败:", str(e))
        return False
    
    try:
        sensor.reset()
        print("✅ 传感器重置成功")
    except Exception as e:
        print("❌ 传感器重置失败:", str(e))
        return False
    
    # 优先使用兼容性最好的格式
    pixel_format = None
    try:
        sensor.set_pixformat(sensor.GRAYSCALE)
        pixel_format = 'GRAYSCALE'
        print("✅ 使用GRAYSCALE格式(兼容性最好)")
    except Exception as e:
        try:
            sensor.set_pixformat(sensor.RGB565)
            pixel_format = 'RGB565'
            print("✅ 使用RGB565格式")
        except Exception as e2:
            print("❌ 像素格式设置失败:", str(e), str(e2))
            return False
    
    # 设置分辨率(优先使用较小分辨率)
    try:
        sensor.set_framesize(sensor.QVGA)  # 320x240
        print("✅ 使用QVGA分辨率(320x240)")
    except Exception as e:
        try:
            sensor.set_framesize(sensor.QQVGA)  # 160x120
            print("✅ 使用QQVGA分辨率(160x120)")
        except Exception as e2:
            print("❌ 分辨率设置失败:", str(e), str(e2))
            return False
    
    try:
        sensor.set_vflip(1)
        sensor.skip_frames(time=2000)  # 等待2秒稳定
        sensor.run(1)
        print("✅ 传感器启动成功")
        return pixel_format
    except Exception as e:
        print("❌ 传感器启动失败:", str(e))
        return False

def safe_detect(img):
    """安全的黑色矩形检测"""
    rectangles = []
    
    try:
        # 使用灰度阈值(兼容性最好，避免to_lab错误)
        threshold = [(0, 30)]  # 黑色阈值
        
        # 查找黑色区域
        blobs = img.find_blobs(threshold, 
                               pixels_threshold=300,  # 最小像素数
                               merge=True)            # 合并相邻区域
        
        for blob in blobs:
            # 基本过滤
            area = blob.area()
            w, h = blob.w(), blob.h()
            
            # 面积过滤
            if area < 200 or area > 20000:
                continue
            
            # 计算矩形度
            rect_ratio = area / (w * h)
            if rect_ratio < 0.5:  # 矩形度太低
                continue
            
            # 计算长宽比
            aspect_ratio = max(w, h) / min(w, h)
            if aspect_ratio > 6:  # 长宽比太大
                continue
            
            # 添加到结果
            rectangles.append({
                'blob': blob,
                'area': area,
                'w': w, 'h': h,
                'x': blob.x(), 'y': blob.y(),
                'cx': blob.cx(), 'cy': blob.cy(),
                'rect_ratio': rect_ratio,
                'aspect_ratio': aspect_ratio
            })
        
        # 按面积排序(大的在前)
        rectangles.sort(key=lambda r: r['area'], reverse=True)
        
    except Exception as e:
        print("❌ 检测错误:", str(e))
    
    return rectangles

def safe_draw(img, rectangles):
    """安全的绘制函数"""
    try:
        for i, rect in enumerate(rectangles):
            blob = rect['blob']
            
            # 绘制检测框
            try:
                img.draw_rectangle(blob.rect(), color=(0, 255, 0), thickness=2)
            except:
                # 如果draw_rectangle失败，尝试手动绘制
                x, y, w, h = rect['x'], rect['y'], rect['w'], rect['h']
                img.draw_line(x, y, x+w, y, color=(0, 255, 0), thickness=2)
                img.draw_line(x+w, y, x+w, y+h, color=(0, 255, 0), thickness=2)
                img.draw_line(x+w, y+h, x, y+h, color=(0, 255, 0), thickness=2)
                img.draw_line(x, y+h, x, y, color=(0, 255, 0), thickness=2)
            
            # 绘制中心点
            try:
                img.draw_cross(rect['cx'], rect['cy'], color=(255, 0, 0), size=8, thickness=2)
            except:
                pass  # 如果绘制失败就跳过
            
            # 绘制信息
            try:
                info_text = "矩形{}".format(i + 1)
                img.draw_string(rect['x'], rect['y'] - 15, info_text, 
                              color=(255, 255, 255), scale=1)
                
                size_text = "{}x{}".format(rect['w'], rect['h'])
                img.draw_string(rect['x'], rect['y'] - 5, size_text, 
                              color=(255, 255, 255), scale=1)
            except:
                pass  # 如果绘制失败就跳过
        
    except Exception as e:
        print("❌ 绘制错误:", str(e))

def main():
    """主函数"""
    print("K210黑色矩形识别 - 错误修复版")
    print("=" * 40)
    print("特性:")
    print("- 解决'Image' object has no attribute 'to_lab'错误")
    print("- 自动选择兼容的像素格式")
    print("- 安全的错误处理")
    print("- 简化的检测算法")
    print()
    
    # 安全初始化
    pixel_format = safe_init()
    if not pixel_format:
        print("❌ 硬件初始化失败，程序退出")
        return
    
    print("✅ 硬件初始化成功，像素格式:", pixel_format)
    print("开始黑色矩形检测...")
    print("按Ctrl+C停止程序")
    print()
    
    # 初始化计时器和计数器
    clock = time.clock()
    frame_count = 0
    total_rectangles = 0
    error_count = 0
    
    try:
        while True:
            clock.tick()
            
            # 安全获取图像
            try:
                img = sensor.snapshot()
                if not img:
                    print("⚠️ 图像获取失败，跳过此帧")
                    continue
            except Exception as e:
                error_count += 1
                print("❌ 图像获取错误:", str(e))
                if error_count > 10:
                    print("❌ 连续错误过多，程序退出")
                    break
                continue
            
            # 重置错误计数
            error_count = 0
            
            # 执行检测
            rectangles = safe_detect(img)
            rect_count = len(rectangles)
            
            # 绘制结果
            safe_draw(img, rectangles)
            
            # 显示状态信息
            try:
                fps = clock.fps()
                img.draw_string(5, 5, "FPS:{:.1f}".format(fps), 
                              color=(255, 255, 255), scale=2)
                img.draw_string(5, 25, "帧:{}".format(frame_count), 
                              color=(255, 255, 255), scale=1)
                img.draw_string(5, 40, "矩形:{}".format(rect_count), 
                              color=(0, 255, 255), scale=2)
                img.draw_string(5, 60, "格式:{}".format(pixel_format), 
                              color=(255, 255, 0), scale=1)
                
                # 显示检测状态
                if rect_count > 0:
                    img.draw_string(200, 5, "检测中", color=(0, 255, 0), scale=2)
                    total_rectangles += rect_count
                    
                    # 打印检测信息
                    print("第{}帧: 检测到{}个黑色矩形".format(frame_count, rect_count))
                    for i, rect in enumerate(rectangles):
                        print("  矩形{}: 位置({},{}) 大小{}x{} 面积:{}".format(
                            i + 1, rect['x'], rect['y'], rect['w'], rect['h'], rect['area']))
                else:
                    img.draw_string(200, 5, "搜索中", color=(255, 255, 0), scale=2)
                
                # 显示平均检测数
                if frame_count > 0:
                    avg_rects = total_rectangles / frame_count
                    img.draw_string(5, 80, "平均:{:.1f}".format(avg_rects), 
                                  color=(255, 255, 0), scale=1)
                
            except Exception as e:
                print("⚠️ 状态显示错误:", str(e))
            
            # 显示图像
            try:
                lcd.display(img)
            except Exception as e:
                print("❌ 图像显示错误:", str(e))
            
            # 垃圾回收(每10帧一次)
            if frame_count % 10 == 0:
                gc.collect()
            
            frame_count += 1
            
            # 帧间延迟
            time.sleep_ms(50)
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print("❌ 程序运行出错:", str(e))
    finally:
        # 显示统计信息
        print("\n" + "=" * 40)
        print("运行统计:")
        print("- 总帧数:", frame_count)
        print("- 总检测矩形数:", total_rectangles)
        if frame_count > 0:
            print("- 平均每帧矩形数:", total_rectangles / frame_count)
            print("- 平均FPS:", frame_count / (time.ticks_ms() / 1000) if time.ticks_ms() > 0 else 0)
        
        # 清理资源
        try:
            sensor.shutdown()
            lcd.clear()
            print("✅ 资源清理完成")
        except Exception as e:
            print("清理资源时出错:", str(e))

if __name__ == "__main__":
    main()
