# K210 CanMV人脸识别 - 官方兼容版本
# 基于CanMV官方示例代码

import sensor, image, time, lcd
from maix import KPU
import gc

# 配置参数 - 支持多种存储位置
MODEL_PATHS = [
    "/flash/face_detect_320x240.kmodel",  # 内部Flash存储
    "/sd/face_detect_320x240.kmodel",     # SD卡存储
    "face_detect_320x240.kmodel",         # 当前目录
    "/flash/face_detection.kmodel",       # 备用模型名
    "/sd/face_detection.kmodel"           # 备用SD卡模型
]
THRESHOLD = 0.5  # 检测阈值
NMS_VALUE = 0.2  # 非极大值抑制
CLASSES = 1  # 人脸类别数

# YOLO2锚点 - CanMV官方配置
ANCHOR = (0.1075, 0.126875, 0.126875, 0.175, 0.1465625, 0.2246875, 
          0.1953125, 0.25375, 0.2440625, 0.351875, 0.341875, 0.4721875, 
          0.5078125, 0.6696875, 0.8984375, 1.099687, 2.129062, 2.425937)

def init_hardware():
    """初始化硬件设备"""
    print("初始化LCD...")
    lcd.init()
    
    print("初始化摄像头...")
    sensor.reset()  # 重置并初始化摄像头
    sensor.set_pixformat(sensor.RGB565)  # 设置像素格式为RGB565
    sensor.set_framesize(sensor.QVGA)    # 设置帧大小为QVGA (320x240)
    sensor.skip_frames(time=1000)        # 等待设置生效
    sensor.run(1)  # 启动摄像头
    
    print("硬件初始化完成")

def find_model_file():
    """查找可用的模型文件"""
    import os

    for model_path in MODEL_PATHS:
        try:
            # 检查文件是否存在
            if model_path.startswith('/sd/'):
                # SD卡路径
                try:
                    files = os.listdir('/sd')
                    filename = model_path.split('/')[-1]
                    if filename in files:
                        print("找到SD卡模型:", model_path)
                        return model_path
                except:
                    continue
            elif model_path.startswith('/flash/'):
                # Flash存储路径
                try:
                    files = os.listdir('/flash')
                    filename = model_path.split('/')[-1]
                    if filename in files:
                        print("找到Flash模型:", model_path)
                        return model_path
                except:
                    continue
            else:
                # 当前目录
                try:
                    files = os.listdir('.')
                    if model_path in files:
                        print("找到当前目录模型:", model_path)
                        return model_path
                except:
                    continue
        except:
            continue

    return None

def load_kmodel():
    """加载KPU模型"""
    # 查找模型文件
    model_path = find_model_file()

    if not model_path:
        print("❌ 未找到模型文件!")
        print("请将模型文件放置在以下任一位置:")
        for path in MODEL_PATHS:
            print("  -", path)
        print("\n💡 建议:")
        print("1. 将模型文件复制到内部Flash: /flash/")
        print("2. 或使用SD卡存储")
        print("3. 或运行无模型演示版本")
        return None

    try:
        print("正在加载模型:", model_path)
        kpu = KPU()
        kpu.load_kmodel(model_path)

        # 初始化YOLO2网络
        kpu.init_yolo2(ANCHOR, anchor_num=9, img_w=320, img_h=240,
                       net_w=320, net_h=240, layer_w=10, layer_h=8,
                       threshold=THRESHOLD, nms_value=NMS_VALUE, classes=CLASSES)

        print("✅ 模型加载成功!")
        return kpu
    except Exception as e:
        print("❌ 模型加载失败:", str(e))
        return None

def detect_faces(kpu):
    """人脸检测主循环"""
    clock = time.clock()  # 创建时钟对象跟踪FPS
    frame_count = 0
    
    print("开始人脸检测...")
    print("按Ctrl+C停止程序")
    
    try:
        while True:
            clock.tick()  # 更新FPS时钟
            img = sensor.snapshot()  # 获取图像
            
            # 运行KPU推理
            kpu.run_with_output(img)
            dect = kpu.regionlayer_yolo2()  # 获取检测结果
            
            fps = clock.fps()
            face_count = 0
            
            # 处理检测结果
            if len(dect) > 0:
                for detection in dect:
                    # 绘制人脸框
                    x, y, w, h = detection[0], detection[1], detection[2], detection[3]
                    img.draw_rectangle(x, y, w, h, color=(0, 255, 0), thickness=2)
                    face_count += 1
                    
                    # 打印检测信息
                    print("人脸{}:({},{}) 大小:{}x{}".format(face_count, x, y, w, h))
            
            # 显示FPS
            img.draw_string(0, 0, "{:.1f}fps".format(fps), color=(0, 60, 128), scale=2.0)
            
            # 显示人脸计数
            if face_count > 0:
                img.draw_string(0, 30, "人脸:{}".format(face_count), color=(255, 0, 0), scale=2.0)
                print("第{}帧: 检测到{}个人脸 FPS:{:.1f}".format(frame_count, face_count, fps))
            
            # 显示图像
            lcd.display(img)
            
            # 垃圾回收
            gc.collect()
            frame_count += 1
            
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print("检测过程出错:", str(e))

def cleanup(kpu):
    """清理资源"""
    try:
        if kpu:
            kpu.deinit()
        sensor.shutdown()
        lcd.clear()
        print("资源清理完成")
    except Exception as e:
        print("清理资源时出错:", str(e))

def main():
    """主函数"""
    print("=== CanMV K210人脸识别系统 ===")
    print("基于CanMV官方示例")
    
    # 初始化硬件
    try:
        init_hardware()
    except Exception as e:
        print("硬件初始化失败:", str(e))
        return
    
    # 加载模型
    kpu = load_kmodel()
    if not kpu:
        return
    
    try:
        # 开始检测
        detect_faces(kpu)
    finally:
        # 清理资源
        cleanup(kpu)

if __name__ == "__main__":
    main()
