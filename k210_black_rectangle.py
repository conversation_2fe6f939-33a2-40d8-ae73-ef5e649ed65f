# K210黑色矩形识别 - MicroPython版本
# 使用图像处理算法识别黑色矩形区域

import sensor, image, time, lcd
import gc

# 配置参数
BLACK_THRESHOLD = [(0, 30, -128, 127, -128, 127)]  # 黑色阈值(LAB色彩空间)
MIN_AREA = 500  # 最小矩形面积
MAX_AREA = 50000  # 最大矩形面积
RECT_MARGIN = 5  # 矩形边界扩展
BOX_COLOR = (0, 255, 0)  # 检测框颜色(绿色)
TEXT_COLOR = (255, 255, 255)  # 文字颜色(白色)

def init_hardware():
    """初始化硬件设备"""
    print("初始化LCD...")
    lcd.init()
    lcd.clear(lcd.BLACK)
    
    print("初始化摄像头...")
    sensor.reset()
    
    # 兼容性设置
    try:
        sensor.set_pixformat(sensor.RGB565)
        print("✅ 使用RGB565格式")
    except:
        sensor.set_pixformat(sensor.GRAYSCALE)
        print("✅ 使用GRAYSCALE格式")
    
    try:
        sensor.set_framesize(sensor.QVGA)  # 320x240
        print("✅ 使用QVGA分辨率")
    except:
        sensor.set_framesize(sensor.QQVGA)  # 160x120
        print("✅ 使用QQVGA分辨率")
    
    sensor.set_vflip(1)  # 垂直翻转
    sensor.set_hmirror(0)  # 水平镜像
    sensor.skip_frames(time=2000)  # 等待稳定
    sensor.run(1)
    
    print("✅ 硬件初始化完成")

def find_black_rectangles(img):
    """查找黑色矩形"""
    rectangles = []
    
    try:
        # 转换为LAB色彩空间以更好地检测黑色
        img_lab = img.to_lab()
        
        # 查找黑色区域
        blobs = img_lab.find_blobs(BLACK_THRESHOLD, 
                                   pixels_threshold=MIN_AREA, 
                                   area_threshold=MIN_AREA,
                                   merge=True)
        
        for blob in blobs:
            # 过滤面积
            if MIN_AREA <= blob.area() <= MAX_AREA:
                # 计算矩形度(接近1表示更像矩形)
                rectangularity = blob.area() / (blob.w() * blob.h())
                
                # 过滤形状(矩形度大于0.7)
                if rectangularity > 0.7:
                    # 扩展边界
                    x = max(0, blob.x() - RECT_MARGIN)
                    y = max(0, blob.y() - RECT_MARGIN)
                    w = min(img.width() - x, blob.w() + 2 * RECT_MARGIN)
                    h = min(img.height() - y, blob.h() + 2 * RECT_MARGIN)
                    
                    rectangles.append({
                        'x': x, 'y': y, 'w': w, 'h': h,
                        'area': blob.area(),
                        'rectangularity': rectangularity,
                        'cx': blob.cx(),  # 中心点x
                        'cy': blob.cy(),  # 中心点y
                        'rotation': blob.rotation()  # 旋转角度
                    })
        
        # 按面积排序(大到小)
        rectangles.sort(key=lambda r: r['area'], reverse=True)
        
    except Exception as e:
        print("❌ 矩形检测错误:", str(e))
    
    return rectangles

def draw_rectangle_info(img, rect, index):
    """绘制矩形信息"""
    try:
        x, y, w, h = rect['x'], rect['y'], rect['w'], rect['h']
        
        # 绘制检测框
        img.draw_rectangle(x, y, w, h, color=BOX_COLOR, thickness=2)
        
        # 绘制中心点
        cx, cy = rect['cx'], rect['cy']
        img.draw_cross(cx, cy, color=BOX_COLOR, size=10, thickness=2)
        
        # 绘制角标
        corner_size = min(w, h) // 6
        # 左上角
        img.draw_line(x, y, x + corner_size, y, color=BOX_COLOR, thickness=3)
        img.draw_line(x, y, x, y + corner_size, color=BOX_COLOR, thickness=3)
        # 右下角
        img.draw_line(x + w, y + h, x + w - corner_size, y + h, color=BOX_COLOR, thickness=3)
        img.draw_line(x + w, y + h, x + w, y + h - corner_size, color=BOX_COLOR, thickness=3)
        
        # 显示信息
        info_y = y - 30 if y > 30 else y + h + 5
        img.draw_string(x, info_y, "矩形{}".format(index + 1), color=TEXT_COLOR, scale=1)
        img.draw_string(x, info_y + 15, "{}x{}".format(w, h), color=TEXT_COLOR, scale=1)
        
        # 显示面积和矩形度
        area_text = "面积:{}".format(rect['area'])
        rect_text = "矩形度:{:.2f}".format(rect['rectangularity'])
        
        img.draw_string(x + w + 5, y, area_text, color=(255, 255, 0), scale=1)
        img.draw_string(x + w + 5, y + 15, rect_text, color=(255, 255, 0), scale=1)
        
    except Exception as e:
        print("⚠️ 绘制矩形信息错误:", str(e))

def detect_black_rectangles():
    """黑色矩形检测主循环"""
    clock = time.clock()
    frame_count = 0
    total_rectangles = 0
    
    print("开始黑色矩形检测...")
    print("检测参数:")
    print("- 最小面积:", MIN_AREA)
    print("- 最大面积:", MAX_AREA)
    print("- 黑色阈值:", BLACK_THRESHOLD)
    print("按Ctrl+C停止程序")
    
    try:
        while True:
            clock.tick()
            
            # 安全获取图像
            try:
                img = sensor.snapshot()
                if not img:
                    continue
            except Exception as e:
                print("❌ 图像获取错误:", str(e))
                continue
            
            # 查找黑色矩形
            rectangles = find_black_rectangles(img)
            
            # 绘制检测结果
            rect_count = len(rectangles)
            if rect_count > 0:
                for i, rect in enumerate(rectangles):
                    draw_rectangle_info(img, rect, i)
                    
                    # 打印详细信息
                    print("矩形{}: 位置({},{}) 大小{}x{} 面积:{} 矩形度:{:.2f}".format(
                        i + 1, rect['x'], rect['y'], rect['w'], rect['h'], 
                        rect['area'], rect['rectangularity']))
                
                total_rectangles += rect_count
            
            # 显示状态信息
            fps = clock.fps()
            img.draw_string(5, 5, "FPS:{:.1f}".format(fps), color=TEXT_COLOR, scale=2)
            img.draw_string(5, 25, "帧:{}".format(frame_count), color=TEXT_COLOR, scale=1)
            img.draw_string(5, 40, "矩形:{}".format(rect_count), color=(0, 255, 255), scale=2)
            
            # 显示统计信息
            if frame_count > 0:
                avg_rects = total_rectangles / frame_count
                img.draw_string(5, 60, "平均:{:.1f}".format(avg_rects), color=(255, 255, 0), scale=1)
            
            # 显示检测状态
            if rect_count > 0:
                img.draw_string(200, 5, "检测中", color=(0, 255, 0), scale=2)
                print("第{}帧: 检测到{}个黑色矩形 FPS:{:.1f}".format(frame_count, rect_count, fps))
            else:
                img.draw_string(200, 5, "搜索中", color=(255, 255, 0), scale=2)
            
            # 显示图像
            try:
                lcd.display(img)
            except Exception as e:
                print("❌ 图像显示错误:", str(e))
            
            # 垃圾回收
            if frame_count % 10 == 0:
                gc.collect()
            
            frame_count += 1
            time.sleep_ms(50)  # 控制帧率
            
    except KeyboardInterrupt:
        print("程序被用户中断")
        print("统计信息:")
        print("- 总帧数:", frame_count)
        print("- 总检测矩形数:", total_rectangles)
        if frame_count > 0:
            print("- 平均每帧矩形数:", total_rectangles / frame_count)
    except Exception as e:
        print("❌ 检测过程出错:", str(e))

def test_black_detection():
    """测试黑色检测功能"""
    print("\n=== 黑色检测测试 ===")
    
    try:
        # 获取测试图像
        img = sensor.snapshot()
        if not img:
            print("❌ 无法获取测试图像")
            return False
        
        # 显示原始图像
        img.draw_string(10, 10, "原始图像", color=(255, 255, 255), scale=2)
        lcd.display(img)
        time.sleep_ms(2000)
        
        # 转换为LAB并显示
        img_lab = img.to_lab()
        img_lab.draw_string(10, 10, "LAB色彩空间", color=(255, 255, 255), scale=2)
        lcd.display(img_lab)
        time.sleep_ms(2000)
        
        # 查找黑色区域
        blobs = img_lab.find_blobs(BLACK_THRESHOLD, pixels_threshold=100)
        
        # 在原图上标记黑色区域
        for blob in blobs:
            img.draw_rectangle(blob.rect(), color=(255, 0, 0), thickness=2)
            img.draw_string(blob.x(), blob.y() - 15, "黑色区域", color=(255, 0, 0), scale=1)
        
        img.draw_string(10, 10, "黑色区域检测", color=(255, 255, 255), scale=2)
        lcd.display(img)
        
        print("✅ 黑色检测测试完成，找到{}个黑色区域".format(len(blobs)))
        return True
        
    except Exception as e:
        print("❌ 黑色检测测试失败:", str(e))
        return False

def show_usage():
    """显示使用说明"""
    print("K210黑色矩形识别程序")
    print("=" * 30)
    print("功能:")
    print("1. 实时检测黑色矩形区域")
    print("2. 显示矩形位置、大小、面积")
    print("3. 计算矩形度(形状相似度)")
    print("4. 实时FPS和统计信息")
    print("\n检测原理:")
    print("- 使用LAB色彩空间检测黑色")
    print("- 通过面积过滤噪声")
    print("- 通过矩形度过滤形状")
    print("- 支持多个矩形同时检测")
    print("\n调整参数:")
    print("- MIN_AREA: 最小矩形面积")
    print("- MAX_AREA: 最大矩形面积") 
    print("- BLACK_THRESHOLD: 黑色检测阈值")

def cleanup():
    """清理资源"""
    try:
        sensor.shutdown()
        lcd.clear()
        print("✅ 资源清理完成")
    except Exception as e:
        print("清理资源时出错:", str(e))

def main():
    """主函数"""
    show_usage()
    
    try:
        # 初始化硬件
        init_hardware()
        
        # 测试黑色检测
        print("\n进行黑色检测测试...")
        if test_black_detection():
            time.sleep_ms(2000)
            
            # 开始矩形检测
            detect_black_rectangles()
        else:
            print("❌ 黑色检测测试失败，请检查环境")
        
    except Exception as e:
        print("❌ 程序运行出错:", str(e))
    finally:
        cleanup()

if __name__ == "__main__":
    main()
