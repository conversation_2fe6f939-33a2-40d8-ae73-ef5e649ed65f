# K210人脸识别 - MicroPython兼容版
import sensor, image, time, lcd
from maix import KPU as kpu

# 配置参数
MODEL_PATH = "/sd/face_detection.kmodel"
THRESHOLD = 0.7
BOX_COLOR = (0, 255, 0)
TEXT_COLOR = (255, 255, 255)
ANCHOR = (1.889, 2.5245, 2.9465, 3.94056, 3.99987, 5.3658, 5.155437, 6.92275, 6.718375, 9.01025)

# 初始化硬件
print("初始化硬件...")
lcd.init()
lcd.clear(lcd.BLACK)
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.set_windowing((320, 240))
sensor.set_vflip(1)
sensor.run(1)
print("硬件初始化完成")

# 加载模型
print("加载模型...")
task = kpu.load(MODEL_PATH)
kpu.init_yolo2(task, 0.5, 0.3, 5, ANCHOR)
print("模型加载完成")

print("开始人脸识别...")
frame_count = 0

try:
    while True:
        img = sensor.snapshot()
        code = kpu.run_yolo2(task, img)
        
        if code:
            face_count = 0
            for i in code:
                confidence = i.value()
                if confidence > THRESHOLD:
                    x, y, w, h = i.x(), i.y(), i.w(), i.h()
                    
                    # 绘制人脸框
                    img.draw_rectangle(x, y, w, h, color=BOX_COLOR, thickness=2)
                    
                    # 显示置信度
                    conf_text = str(int(confidence * 100)) + "%"
                    img.draw_string(x, y-20, conf_text, color=TEXT_COLOR, scale=2)
                    
                    face_count += 1
                    print("检测到人脸: 位置(" + str(x) + "," + str(y) + ") 置信度:" + str(confidence))
            
            if face_count > 0:
                print("第" + str(frame_count) + "帧: 检测到" + str(face_count) + "个人脸")
        
        lcd.display(img)
        frame_count += 1
        time.sleep_ms(50)
        
except KeyboardInterrupt:
    print("程序被用户中断")
except Exception as e:
    print("运行错误: " + str(e))
finally:
    print("清理资源...")
    kpu.deinit(task)
    sensor.shutdown()
    lcd.clear()
    print("程序结束")
