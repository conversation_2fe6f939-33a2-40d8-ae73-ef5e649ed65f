# CanMV K210环境诊断脚本
# 专门针对CanMV固件的环境检查

import sys
import gc

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 40)
    print(title)
    print("=" * 40)

def check_basic_modules():
    """检查基础模块"""
    print_header("检查基础模块")
    
    modules = {
        'sensor': '摄像头模块',
        'image': '图像处理模块', 
        'time': '时间模块',
        'lcd': 'LCD显示模块',
        'gc': '垃圾回收模块'
    }
    
    all_ok = True
    for module, desc in modules.items():
        try:
            __import__(module)
            print("✓ {} ({})".format(module, desc))
        except ImportError:
            print("✗ {} ({}) - 导入失败".format(module, desc))
            all_ok = False
    
    return all_ok

def check_kpu_module():
    """检查KPU模块 - CanMV专用"""
    print_header("检查KPU模块")
    
    try:
        from maix import KPU
        print("✓ KPU模块导入成功 (from maix import KPU)")
        
        # 尝试创建KPU实例
        try:
            kpu = KPU()
            print("✓ KPU实例创建成功")
            
            # 检查KPU方法
            methods = ['load_kmodel', 'init_yolo2', 'run_with_output', 'regionlayer_yolo2', 'deinit']
            for method in methods:
                if hasattr(kpu, method):
                    print("✓ KPU.{} 方法可用".format(method))
                else:
                    print("✗ KPU.{} 方法不可用".format(method))
            
            return True
            
        except Exception as e:
            print("✗ KPU实例创建失败:", str(e))
            return False
            
    except ImportError as e:
        print("✗ KPU模块导入失败:", str(e))
        print("请确认使用的是CanMV固件")
        return False

def check_hardware():
    """检查硬件状态"""
    print_header("检查硬件状态")
    
    try:
        import sensor, lcd
        
        # 检查LCD
        try:
            lcd.init()
            print("✓ LCD初始化成功")
            lcd.clear(lcd.BLACK)
            print("✓ LCD清屏成功")
        except Exception as e:
            print("✗ LCD错误:", str(e))
            return False
        
        # 检查摄像头
        try:
            sensor.reset()
            print("✓ 摄像头重置成功")
            
            sensor.set_pixformat(sensor.RGB565)
            sensor.set_framesize(sensor.QVGA)
            print("✓ 摄像头配置成功")
            
            sensor.run(1)
            print("✓ 摄像头启动成功")
            
            # 尝试获取一帧图像
            img = sensor.snapshot()
            if img:
                print("✓ 图像获取成功 - 尺寸: {}x{}".format(img.width(), img.height()))
            else:
                print("✗ 图像获取失败")
                return False
                
        except Exception as e:
            print("✗ 摄像头错误:", str(e))
            return False
            
        return True
        
    except ImportError as e:
        print("✗ 硬件模块导入失败:", str(e))
        return False

def check_sd_card():
    """检查SD卡和模型文件"""
    print_header("检查SD卡和模型文件")
    
    try:
        import os
        
        # 检查SD卡挂载
        try:
            files = os.listdir("/sd")
            print("✓ SD卡可访问")
            print("SD卡文件数量:", len(files))
            
            # 检查常见模型文件
            model_files = [
                "face_detect_320x240.kmodel",
                "face_detection.kmodel", 
                "yolo2_face.kmodel"
            ]
            
            found_models = []
            for model in model_files:
                if model in files:
                    found_models.append(model)
                    try:
                        stat = os.stat("/sd/" + model)
                        size = stat[6]
                        print("✓ 找到模型: {} (大小: {} 字节)".format(model, size))
                    except:
                        print("? 模型文件信息获取失败:", model)
            
            if found_models:
                print("✓ 共找到 {} 个模型文件".format(len(found_models)))
                return True
            else:
                print("✗ 未找到人脸检测模型文件")
                print("请将以下任一模型文件放入SD卡:")
                for model in model_files:
                    print("  - " + model)
                return False
                
        except Exception as e:
            print("✗ SD卡访问失败:", str(e))
            return False
            
    except ImportError:
        print("✗ os模块不可用")
        return False

def check_memory():
    """检查内存状态"""
    print_header("检查内存状态")
    
    try:
        # 执行垃圾回收
        gc.collect()
        
        # 获取内存信息
        free_mem = gc.mem_free()
        alloc_mem = gc.mem_alloc()
        total_mem = free_mem + alloc_mem
        usage_percent = int(alloc_mem * 100 / total_mem)
        
        print("总内存: {} KB".format(total_mem // 1024))
        print("已用内存: {} KB ({}%)".format(alloc_mem // 1024, usage_percent))
        print("可用内存: {} KB".format(free_mem // 1024))
        
        if free_mem > 200000:  # 200KB
            print("✓ 可用内存充足")
            return True
        elif free_mem > 100000:  # 100KB
            print("⚠ 可用内存一般，建议释放一些资源")
            return True
        else:
            print("✗ 可用内存不足，可能影响模型加载")
            return False
            
    except Exception as e:
        print("✗ 内存检查失败:", str(e))
        return False

def print_system_info():
    """打印系统信息"""
    print_header("系统信息")
    
    try:
        print("Python版本:", sys.version)
        print("平台:", sys.platform)
        
        # 检查CanMV特有模块
        canmv_modules = ['machine', 'fpioa_manager', 'board']
        for module in canmv_modules:
            try:
                __import__(module)
                print("✓ {} 模块可用".format(module))
            except ImportError:
                print("- {} 模块不可用".format(module))
                
    except Exception as e:
        print("获取系统信息失败:", str(e))

def run_quick_test():
    """运行快速功能测试"""
    print_header("快速功能测试")
    
    try:
        from maix import KPU
        import sensor, lcd, image
        
        print("正在进行快速硬件测试...")
        
        # 初始化硬件
        lcd.init()
        sensor.reset()
        sensor.set_pixformat(sensor.RGB565)
        sensor.set_framesize(sensor.QVGA)
        sensor.run(1)
        
        # 获取几帧图像
        for i in range(3):
            img = sensor.snapshot()
            if img:
                lcd.display(img)
                print("✓ 第{}帧图像显示成功".format(i+1))
            else:
                print("✗ 第{}帧图像获取失败".format(i+1))
                return False
        
        print("✓ 硬件功能测试通过")
        return True
        
    except Exception as e:
        print("✗ 功能测试失败:", str(e))
        return False

def main():
    """主诊断函数"""
    print("CanMV K210环境诊断工具")
    print("专为CanMV固件设计")
    
    # 执行各项检查
    results = []
    results.append(("基础模块", check_basic_modules()))
    results.append(("KPU模块", check_kpu_module()))
    results.append(("硬件状态", check_hardware()))
    results.append(("SD卡检查", check_sd_card()))
    results.append(("内存状态", check_memory()))
    results.append(("功能测试", run_quick_test()))
    
    # 打印系统信息
    print_system_info()
    
    # 总结结果
    print_header("诊断结果总结")
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print("{}: {}".format(name, status))
        if result:
            passed += 1
    
    print("\n通过率: {}/{} ({:.1f}%)".format(passed, total, passed*100/total))
    
    if passed == total:
        print("\n🎉 所有检查通过！可以运行人脸识别程序")
        print("推荐运行: exec(open('k210_canmv_face.py').read())")
    else:
        print("\n⚠️ 存在问题，请根据上述信息进行修复")
        print("\n常见解决方案:")
        print("1. 确认使用CanMV固件 (不是MaixPy)")
        print("2. 检查硬件连接")
        print("3. 确认SD卡正确插入并包含模型文件")
        print("4. 重启开发板后重试")

if __name__ == "__main__":
    main()
