# K210黑色矩形识别 - 使用统一配置文件
# 从config.py导入配置参数，避免重复定义

import sensor, image, time, lcd
import gc
from config import BlackRectConfig as cfg

def init_hardware():
    """初始化硬件设备"""
    print("初始化硬件...")
    lcd.init()
    lcd.clear(lcd.BLACK)
    
    sensor.reset()
    
    # 兼容性设置(优先使用灰度模式)
    try:
        sensor.set_pixformat(sensor.GRAYSCALE)
        print("✅ 使用GRAYSCALE格式(兼容性最好)")
    except:
        sensor.set_pixformat(sensor.RGB565)
        print("✅ 使用RGB565格式")
    
    try:
        sensor.set_framesize(sensor.QVGA)
        print("✅ 使用QVGA分辨率")
    except:
        sensor.set_framesize(sensor.QQVGA)
        print("✅ 使用QQVGA分辨率")
    
    sensor.set_vflip(1)
    sensor.skip_frames(time=2000)
    sensor.run(1)
    
    print("✅ 硬件初始化完成")

def find_black_rectangles(img):
    """查找黑色矩形"""
    rectangles = []
    
    try:
        # 使用配置的阈值
        blobs = img.find_blobs(cfg.BLACK_THRESHOLD_GRAY, 
                               pixels_threshold=cfg.MIN_AREA, 
                               merge=True)
        
        for blob in blobs:
            # 面积过滤
            if not (cfg.MIN_AREA <= blob.area() <= cfg.MAX_AREA):
                continue
            
            # 矩形度过滤
            rectangularity = blob.area() / (blob.w() * blob.h())
            if rectangularity < cfg.MIN_RECTANGULARITY:
                continue
            
            # 长宽比过滤
            aspect_ratio = max(blob.w(), blob.h()) / min(blob.w(), blob.h())
            if aspect_ratio > cfg.MAX_ASPECT_RATIO:
                continue
            
            # 扩展边界
            x = max(0, blob.x() - cfg.RECT_MARGIN)
            y = max(0, blob.y() - cfg.RECT_MARGIN)
            w = min(img.width() - x, blob.w() + 2 * cfg.RECT_MARGIN)
            h = min(img.height() - y, blob.h() + 2 * cfg.RECT_MARGIN)
            
            rectangles.append({
                'x': x, 'y': y, 'w': w, 'h': h,
                'area': blob.area(),
                'rectangularity': rectangularity,
                'aspect_ratio': aspect_ratio,
                'cx': blob.cx(),
                'cy': blob.cy()
            })
        
        # 按面积排序
        rectangles.sort(key=lambda r: r['area'], reverse=True)
        
    except Exception as e:
        print("❌ 矩形检测错误:", str(e))
    
    return rectangles

def draw_rectangle_info(img, rect, index):
    """绘制矩形信息"""
    try:
        x, y, w, h = rect['x'], rect['y'], rect['w'], rect['h']
        cx, cy = rect['cx'], rect['cy']
        
        # 绘制检测框
        img.draw_rectangle(x, y, w, h, color=cfg.BOX_COLOR, thickness=cfg.BOX_THICKNESS)
        
        # 绘制中心点
        img.draw_cross(cx, cy, color=cfg.CENTER_COLOR, size=10, thickness=2)
        
        # 绘制对角线
        img.draw_line(x, y, x+w, y+h, color=cfg.DIAGONAL_COLOR, thickness=1)
        img.draw_line(x+w, y, x, y+h, color=cfg.DIAGONAL_COLOR, thickness=1)
        
        # 显示基本信息
        info_x = x + cfg.INFO_OFFSET_X
        info_y = y - 30 if y > 30 else y + h + cfg.INFO_OFFSET_Y
        
        img.draw_string(info_x, info_y, "矩形{}".format(index + 1), 
                       color=cfg.TEXT_COLOR, scale=1)
        img.draw_string(info_x, info_y + 12, "{}x{}".format(w, h), 
                       color=cfg.TEXT_COLOR, scale=1)
        
        # 显示详细信息
        detail_x = x + w + cfg.INFO_OFFSET_X
        detail_y = y
        
        img.draw_string(detail_x, detail_y, "面积:{}".format(rect['area']), 
                       color=(255, 255, 0), scale=1)
        img.draw_string(detail_x, detail_y + 12, "矩形度:{:.2f}".format(rect['rectangularity']), 
                       color=(255, 0, 255), scale=1)
        img.draw_string(detail_x, detail_y + 24, "长宽比:{:.1f}".format(rect['aspect_ratio']), 
                       color=(0, 255, 255), scale=1)
        
        # 在矩形中心显示编号
        img.draw_string(cx-5, cy-5, str(index+1), color=cfg.TEXT_COLOR, scale=2)
        
    except Exception as e:
        print("⚠️ 绘制矩形信息错误:", str(e))

def detect_black_rectangles():
    """黑色矩形检测主循环"""
    clock = time.clock()
    frame_count = 0
    total_rectangles = 0
    
    print("开始黑色矩形检测...")
    print("配置参数:")
    print("- 最小面积:", cfg.MIN_AREA)
    print("- 最大面积:", cfg.MAX_AREA)
    print("- 最小矩形度:", cfg.MIN_RECTANGULARITY)
    print("- 最大长宽比:", cfg.MAX_ASPECT_RATIO)
    print("按Ctrl+C停止程序")
    
    try:
        while True:
            clock.tick()
            
            # 安全获取图像
            try:
                img = sensor.snapshot()
                if not img:
                    continue
            except Exception as e:
                print("❌ 图像获取错误:", str(e))
                continue
            
            # 查找黑色矩形
            rectangles = find_black_rectangles(img)
            
            # 绘制检测结果
            rect_count = len(rectangles)
            if rect_count > 0:
                for i, rect in enumerate(rectangles):
                    draw_rectangle_info(img, rect, i)
                
                total_rectangles += rect_count
                
                # 打印详细信息
                print("第{}帧: 检测到{}个黑色矩形".format(frame_count, rect_count))
                for i, rect in enumerate(rectangles):
                    print("  矩形{}: 位置({},{}) 大小{}x{} 面积:{} 矩形度:{:.2f}".format(
                        i + 1, rect['x'], rect['y'], rect['w'], rect['h'], 
                        rect['area'], rect['rectangularity']))
            
            # 显示状态信息
            fps = clock.fps()
            img.draw_string(5, 5, "FPS:{:.1f}".format(fps), color=cfg.TEXT_COLOR, scale=2)
            img.draw_string(5, 25, "帧:{}".format(frame_count), color=cfg.TEXT_COLOR, scale=1)
            img.draw_string(5, 40, "矩形:{}".format(rect_count), color=(0, 255, 255), scale=2)
            
            # 显示统计信息
            if frame_count > 0:
                avg_rects = total_rectangles / frame_count
                img.draw_string(5, 60, "平均:{:.1f}".format(avg_rects), color=(255, 255, 0), scale=1)
            
            # 显示配置信息
            img.draw_string(5, 220, "最小面积:{}".format(cfg.MIN_AREA), color=(128, 128, 128), scale=1)
            
            # 显示检测状态
            if rect_count > 0:
                img.draw_string(200, 5, "检测中", color=(0, 255, 0), scale=2)
            else:
                img.draw_string(200, 5, "搜索中", color=(255, 255, 0), scale=2)
            
            # 显示图像
            try:
                lcd.display(img)
            except Exception as e:
                print("❌ 图像显示错误:", str(e))
            
            # 垃圾回收
            if frame_count % cfg.GC_INTERVAL == 0:
                gc.collect()
            
            frame_count += 1
            time.sleep_ms(cfg.FRAME_DELAY_MS)
            
    except KeyboardInterrupt:
        print("程序被用户中断")
        print("统计信息:")
        print("- 总帧数:", frame_count)
        print("- 总检测矩形数:", total_rectangles)
        if frame_count > 0:
            print("- 平均每帧矩形数:", total_rectangles / frame_count)
    except Exception as e:
        print("❌ 检测过程出错:", str(e))

def show_config():
    """显示配置信息"""
    print("K210黑色矩形识别程序 - 配置版")
    print("=" * 35)
    print("检测配置:")
    print("- 黑色阈值:", cfg.BLACK_THRESHOLD_GRAY)
    print("- 最小面积:", cfg.MIN_AREA)
    print("- 最大面积:", cfg.MAX_AREA)
    print("- 最小矩形度:", cfg.MIN_RECTANGULARITY)
    print("- 最大长宽比:", cfg.MAX_ASPECT_RATIO)
    
    print("\n绘制配置:")
    print("- 检测框颜色:", cfg.BOX_COLOR)
    print("- 文字颜色:", cfg.TEXT_COLOR)
    print("- 中心点颜色:", cfg.CENTER_COLOR)
    print("- 框线粗细:", cfg.BOX_THICKNESS)
    
    print("\n性能配置:")
    print("- 帧间延迟:", cfg.FRAME_DELAY_MS, "ms")
    print("- 垃圾回收间隔:", cfg.GC_INTERVAL, "帧")

def cleanup():
    """清理资源"""
    try:
        sensor.shutdown()
        lcd.clear()
        print("✅ 资源清理完成")
    except Exception as e:
        print("清理资源时出错:", str(e))

def main():
    """主函数"""
    show_config()
    
    try:
        # 初始化硬件
        init_hardware()
        
        # 开始检测
        detect_black_rectangles()
        
    except Exception as e:
        print("❌ 程序运行出错:", str(e))
    finally:
        cleanup()

if __name__ == "__main__":
    main()
